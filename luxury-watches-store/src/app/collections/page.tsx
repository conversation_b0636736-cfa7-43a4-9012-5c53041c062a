'use client'

import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { ArrowRight, Calendar, Star, Award } from 'lucide-react'
import Head from 'next/head'

const collections = [
  {
    id: 'vintage-classics',
    name: 'Vintage Classics',
    description: 'Timeless pieces that have defined horological excellence for decades',
    image: 'https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=600&h=400&fit=crop&crop=center',
    itemCount: 24,
    priceRange: '$15,000 - $150,000',
    featured: true
  },
  {
    id: 'modern-sports',
    name: 'Modern Sports',
    description: 'Contemporary timepieces designed for active lifestyles and adventure',
    image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&h=400&fit=crop&crop=center',
    itemCount: 18,
    priceRange: '$8,000 - $50,000',
    featured: true
  },
  {
    id: 'dress-elegance',
    name: 'Dress Elegance',
    description: 'Sophisticated timepieces perfect for formal occasions and business',
    image: 'https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=600&h=400&fit=crop&crop=center',
    itemCount: 16,
    priceRange: '$12,000 - $80,000',
    featured: false
  },
  {
    id: 'complications-masters',
    name: 'Complications Masters',
    description: 'Exceptional timepieces showcasing the pinnacle of watchmaking artistry',
    image: 'https://images.unsplash.com/photo-1509048191080-d2e2678e67b4?w=600&h=400&fit=crop&crop=center',
    itemCount: 12,
    priceRange: '$25,000 - $500,000',
    featured: true
  },
  {
    id: 'limited-editions',
    name: 'Limited Editions',
    description: 'Exclusive timepieces with limited production runs for discerning collectors',
    image: 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=600&h=400&fit=crop&crop=center',
    itemCount: 8,
    priceRange: '$30,000 - $200,000',
    featured: false
  },
  {
    id: 'womens-luxury',
    name: "Women's Luxury",
    description: 'Exquisite timepieces designed specifically for the sophisticated woman',
    image: 'https://images.unsplash.com/photo-1587836374828-4dbafa94cf0e?w=600&h=400&fit=crop&crop=center',
    itemCount: 20,
    priceRange: '$10,000 - $100,000',
    featured: false
  }
]

export default function CollectionsPage() {
  const featuredCollections = collections.filter(c => c.featured)
  const otherCollections = collections.filter(c => !c.featured)

  return (
    <>
      <Head>
        <title>Watch Collections - Luxury Watches Store</title>
        <meta name="description" content="Explore curated collections of luxury watches, from vintage classics to modern masterpieces." />
        <meta property="og:title" content="Watch Collections - Luxury Watches Store" />
        <meta property="og:description" content="Explore curated collections of luxury watches, from vintage classics to modern masterpieces." />
        <meta property="og:type" content="website" />
      </Head>
      <div className="min-h-screen bg-luxury-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-luxury-cream to-luxury-white py-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="font-luxury-serif text-5xl md:text-6xl font-bold text-luxury-black mb-6">
              Curated <span className="text-luxury-gold">Collections</span>
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Discover carefully curated collections of luxury timepieces, each telling a unique story of craftsmanship and heritage
            </p>
          </div>
        </div>
      </section>

      {/* Featured Collections */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-4">
              Featured Collections
            </h2>
            <p className="text-xl text-gray-600">
              Our most prestigious and sought-after timepiece collections
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8 mb-20">
            {featuredCollections.map((collection, index) => (
              <motion.div
                key={collection.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                className="bg-white rounded-xl shadow-luxury hover:shadow-luxury-hover transition-all duration-300 overflow-hidden group"
              >
                <div className="relative h-64 overflow-hidden">
                  <Image
                    src={collection.image}
                    alt={collection.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute top-4 right-4">
                    <div className="flex items-center gap-1 px-3 py-1 bg-luxury-gold text-luxury-black text-sm font-semibold rounded-full">
                      <Star className="w-4 h-4" />
                      Featured
                    </div>
                  </div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="font-luxury-serif text-2xl font-bold mb-2">
                      {collection.name}
                    </h3>
                    <p className="text-sm opacity-90">
                      {collection.itemCount} timepieces
                    </p>
                  </div>
                </div>
                
                <div className="p-6">
                  <p className="text-gray-600 mb-4">
                    {collection.description}
                  </p>
                  
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Award className="w-4 h-4 text-luxury-gold" />
                      <span className="text-sm text-gray-500">Price Range</span>
                    </div>
                    <span className="font-semibold text-luxury-black">
                      {collection.priceRange}
                    </span>
                  </div>
                  
                  <Link
                    href={`/collections/${collection.id}`}
                    className="inline-flex items-center gap-2 w-full justify-center px-6 py-3 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors group-hover:gap-3"
                  >
                    Explore Collection
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Other Collections */}
      <section className="py-20 bg-luxury-cream">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-4">
              All Collections
            </h2>
            <p className="text-xl text-gray-600">
              Discover our complete range of luxury timepiece collections
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {otherCollections.map((collection, index) => (
              <motion.div
                key={collection.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-luxury hover:shadow-luxury-hover transition-all duration-300 overflow-hidden group"
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={collection.image}
                    alt={collection.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="font-luxury-serif text-xl font-bold">
                      {collection.name}
                    </h3>
                  </div>
                </div>
                
                <div className="p-6">
                  <p className="text-gray-600 mb-4 text-sm">
                    {collection.description}
                  </p>
                  
                  <div className="flex items-center justify-between mb-4 text-sm">
                    <span className="text-gray-500">
                      {collection.itemCount} pieces
                    </span>
                    <span className="font-semibold text-luxury-black">
                      {collection.priceRange}
                    </span>
                  </div>
                  
                  <Link
                    href={`/collections/${collection.id}`}
                    className="inline-flex items-center gap-2 text-luxury-gold hover:text-luxury-gold-dark font-semibold group-hover:gap-3 transition-all"
                  >
                    View Collection
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-6">
            Can't Find What You're Looking For?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Browse our complete catalog or contact our experts for personalized recommendations
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/catalog"
              className="inline-flex items-center gap-2 px-8 py-4 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors"
            >
              Browse All Watches
              <ArrowRight className="w-5 h-5" />
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center gap-2 px-8 py-4 border-2 border-luxury-gold text-luxury-gold font-semibold rounded-lg hover:bg-luxury-gold hover:text-luxury-black transition-colors"
            >
              Contact Expert
            </Link>
          </div>
        </div>
      </section>
      </div>
    </>
  )
}
