'use client';

import { useState, useEffect } from 'react';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Heart, 
  ShoppingBag, 
  Share2, 
  Star, 
  ChevronLeft, 
  ChevronRight,
  Truck,
  Shield,
  RotateCcw,
  Award
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { WatchCard } from '@/components/product/watch-card';
import { ImageGallery } from '@/components/product/image-gallery';
import { useCart } from '@/contexts/cart-context';
import { formatPrice, cn } from '@/lib/utils';

// Mock data - in real app this would come from API
const MOCK_WATCH_DETAILS = {
  '1': {
    id: '1',
    name: 'Submariner Date "Hulk"',
    brand: 'Rolex',
    price: 18500,
    originalPrice: null,
    images: [
      '/images/watches/rolex-submariner-1.webp',
      '/images/watches/rolex-submariner-2.webp',
      '/images/watches/rolex-submariner-3.webp',
      '/images/watches/rolex-submariner-4.webp',
      '/images/watches/rolex-submariner-5.webp',
    ],
    category: 'diving',
    description: 'The legendary Rolex Submariner Date in the iconic "Hulk" configuration. This stunning timepiece features a vibrant green dial and matching green Cerachrom bezel, making it one of the most sought-after sports watches in the world. Discontinued in 2020, the Hulk has become a true collector\'s piece.',
    features: [
      'Water resistant to 300m (1,000 feet)',
      'Automatic, self-winding movement',
      'Green Cerachrom unidirectional rotatable bezel',
      'Green sunray-finish dial',
      'Luminescent Chromalight hour markers and hands',
      'Oyster bracelet with Oysterlock safety clasp',
      'Certified Swiss chronometer'
    ],
    specifications: {
      'Case Material': 'Oystersteel',
      'Case Diameter': '40mm',
      'Case Thickness': '12.5mm',
      'Dial Color': 'Green sunray-finish',
      'Movement': 'Calibre 3235, Automatic',
      'Power Reserve': 'Approximately 70 hours',
      'Water Resistance': '300m (1,000 feet)',
      'Crystal': 'Sapphire, anti-reflective coating',
      'Bracelet': 'Oystersteel Oyster bracelet',
      'Clasp': 'Oysterlock safety clasp with Glidelock extension',
      'Functions': 'Hours, minutes, seconds, date',
      'Warranty': '5 years international warranty'
    },
    inStock: true,
    isNew: false,
    isBestseller: true,
    rating: 4.8,
    reviewCount: 127,
    sku: 'ROL-SUB-116610LV'
  }
};

const RELATED_WATCHES = [
  {
    id: '2',
    name: 'GMT-Master II',
    brand: 'Rolex',
    price: 15200,
    originalPrice: null,
    images: ['/images/watches/rolex-submariner-2.webp'],
    category: 'gmt',
    description: 'Professional GMT watch for travelers',
    features: ['Dual time zone', 'Ceramic bezel', 'Automatic movement'],
    specifications: {
      caseSize: '40mm',
      caseMaterial: 'Stainless Steel',
      movement: 'Automatic',
      waterResistance: '100m',
      crystal: 'Sapphire'
    },
    inStock: true,
    isNew: false,
    isBestseller: true,
  },
  {
    id: '3',
    name: 'Seamaster Planet Ocean',
    brand: 'Omega',
    price: 8900,
    originalPrice: null,
    images: ['/images/watches/placeholder.svg'],
    category: 'diving',
    description: 'Professional diving watch with Co-Axial movement',
    features: ['Water resistant to 600m', 'Co-Axial escapement', 'Helium escape valve'],
    specifications: {
      caseSize: '43.5mm',
      caseMaterial: 'Stainless Steel',
      movement: 'Co-Axial',
      waterResistance: '600m',
      crystal: 'Sapphire'
    },
    inStock: true,
    isNew: true,
    isBestseller: false,
  }
];

interface ProductPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

export default function ProductPage({ params }: ProductPageProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState('description');
  const [watchId, setWatchId] = useState<string>('1'); // Default to '1'
  const { addItem, openCart } = useCart();

  useEffect(() => {
    const getParams = async () => {
      try {
        const resolvedParams = await params;
        setWatchId(resolvedParams.id);
      } catch (error) {
        console.error('Error resolving params:', error);
        // Keep default watchId = '1'
      }
    };
    getParams();
  }, [params]);

  const watch = MOCK_WATCH_DETAILS[watchId as keyof typeof MOCK_WATCH_DETAILS];

  // Show loading state while watchId is being resolved
  if (!watchId || !watch) {
    return (
      <div className="min-h-screen bg-luxury-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-luxury-gold border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading watch details...</p>
        </div>
      </div>
    );
  }

  const handleAddToCart = () => {
    // Convert watch details to SimpleWatch format for cart
    const simpleWatch = {
      id: watch.id,
      name: watch.name,
      brand: watch.brand,
      price: watch.price,
      originalPrice: watch.originalPrice,
      images: watch.images, // Use all images
      category: watch.category,
      description: watch.description,
      features: watch.features,
      inStock: watch.inStock,
      isNew: watch.isNew,
      isBestseller: watch.isBestseller,
    };

    addItem(simpleWatch, quantity);
    openCart();
  };

  const handleWishlistToggle = () => {
    setIsWishlisted(!isWishlisted);
    // Here you would typically update the wishlist
  };

  const nextImage = () => {
    setSelectedImageIndex((prev) => 
      prev === watch.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setSelectedImageIndex((prev) => 
      prev === 0 ? watch.images.length - 1 : prev - 1
    );
  };

  return (
    <div className="min-h-screen bg-luxury-white">
      {/* Breadcrumb */}
      <div className="bg-luxury-cream py-4">
        <div className="container mx-auto px-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-gray-600 hover:text-luxury-gold">
              Home
            </Link>
            <span className="text-gray-400">/</span>
            <Link href="/catalog" className="text-gray-600 hover:text-luxury-gold">
              Catalog
            </Link>
            <span className="text-gray-400">/</span>
            <Link href={`/brands/${watch.brand.toLowerCase()}`} className="text-gray-600 hover:text-luxury-gold">
              {watch.brand}
            </Link>
            <span className="text-gray-400">/</span>
            <span className="text-luxury-black font-medium">{watch.name}</span>
          </nav>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Image Gallery */}
          <div className="relative">
            <ImageGallery
              images={watch.images}
              productName={`${watch.brand} ${watch.name}`}
            />

            {/* Badges */}
            <div className="absolute top-4 left-4 flex flex-col gap-2 z-10">
              {watch.isNew && (
                <span className="bg-luxury-gold text-luxury-black px-3 py-1 rounded-full text-sm font-semibold">
                  NEW
                </span>
              )}
              {watch.isBestseller && (
                <span className="bg-luxury-charcoal text-white px-3 py-1 rounded-full text-sm font-semibold">
                  BESTSELLER
                </span>
              )}
            </div>
          </div>

          {/* Product Information */}
          <div className="space-y-6">
            {/* Header */}
            <div>
              <p className="text-luxury-gold text-sm font-medium uppercase tracking-wide mb-2">
                {watch.brand}
              </p>
              <h1 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-4">
                {watch.name}
              </h1>
              
              {/* Rating */}
              <div className="flex items-center gap-2 mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={cn(
                        "h-4 w-4",
                        i < Math.floor(watch.rating) 
                          ? "text-yellow-400 fill-current" 
                          : "text-gray-300"
                      )}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-600">
                  {watch.rating} ({watch.reviewCount} reviews)
                </span>
              </div>

              {/* SKU */}
              <p className="text-sm text-gray-500 mb-4">
                SKU: {watch.sku}
              </p>
            </div>

            {/* Price */}
            <div className="border-t border-b border-gray-200 py-6">
              <div className="flex items-center gap-4">
                {watch.originalPrice && (
                  <span className="text-gray-400 line-through text-xl">
                    {formatPrice(watch.originalPrice)}
                  </span>
                )}
                <span className="text-3xl font-bold text-luxury-gold font-luxury-serif">
                  {formatPrice(watch.price)}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Price includes VAT. Free shipping on orders over $1,000.
              </p>
            </div>

            {/* Stock Status */}
            <div className="flex items-center gap-2">
              <div className={cn(
                "w-3 h-3 rounded-full",
                watch.inStock ? "bg-green-500" : "bg-red-500"
              )} />
              <span className={cn(
                "font-medium",
                watch.inStock ? "text-green-600" : "text-red-600"
              )}>
                {watch.inStock ? "In Stock" : "Out of Stock"}
              </span>
            </div>

            {/* Actions */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Button
                  onClick={handleAddToCart}
                  disabled={!watch.inStock}
                  className="flex-1 h-12"
                  variant={watch.inStock ? "luxury" : "secondary"}
                >
                  <ShoppingBag className="h-5 w-5 mr-2" />
                  {watch.inStock ? "Add to Cart" : "Out of Stock"}
                </Button>
                
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleWishlistToggle}
                  className={cn(
                    "h-12 w-12",
                    isWishlisted && "text-red-500 border-red-500"
                  )}
                >
                  <Heart className={cn("h-5 w-5", isWishlisted && "fill-current")} />
                </Button>

                <Button variant="outline" size="icon" className="h-12 w-12">
                  <Share2 className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Features */}
            <div className="bg-luxury-cream rounded-lg p-6">
              <h3 className="font-semibold text-luxury-black mb-4">Key Features</h3>
              <ul className="space-y-2">
                {watch.features.slice(0, 4).map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-700">
                    <div className="w-2 h-2 bg-luxury-gold rounded-full mr-3" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Service Icons */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-6">
              <div className="text-center">
                <Truck className="h-8 w-8 text-luxury-gold mx-auto mb-2" />
                <p className="text-xs text-gray-600">Free Shipping</p>
              </div>
              <div className="text-center">
                <Shield className="h-8 w-8 text-luxury-gold mx-auto mb-2" />
                <p className="text-xs text-gray-600">5 Year Warranty</p>
              </div>
              <div className="text-center">
                <RotateCcw className="h-8 w-8 text-luxury-gold mx-auto mb-2" />
                <p className="text-xs text-gray-600">30 Day Returns</p>
              </div>
              <div className="text-center">
                <Award className="h-8 w-8 text-luxury-gold mx-auto mb-2" />
                <p className="text-xs text-gray-600">Authentic</p>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8">
              {[
                { id: 'description', label: 'Description' },
                { id: 'specifications', label: 'Specifications' },
                { id: 'reviews', label: 'Reviews' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "py-4 px-1 border-b-2 font-medium text-sm transition-colors",
                    activeTab === tab.id
                      ? "border-luxury-gold text-luxury-gold"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  )}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="py-8">
            {activeTab === 'description' && (
              <div className="prose max-w-none">
                <p className="text-gray-700 leading-relaxed mb-6">
                  {watch.description}
                </p>

                <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black mb-4">
                  Features & Benefits
                </h3>
                <ul className="space-y-3">
                  {watch.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <div className="w-2 h-2 bg-luxury-gold rounded-full mt-2 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {activeTab === 'specifications' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(watch.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-3 border-b border-gray-100">
                    <span className="font-medium text-gray-900">{key}</span>
                    <span className="text-gray-700">{value}</span>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black">
                    Customer Reviews
                  </h3>
                  <Button variant="outline">Write a Review</Button>
                </div>

                {/* Review Summary */}
                <div className="bg-luxury-cream rounded-lg p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="text-3xl font-bold text-luxury-black">
                      {watch.rating}
                    </div>
                    <div>
                      <div className="flex items-center mb-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={cn(
                              "h-4 w-4",
                              i < Math.floor(watch.rating)
                                ? "text-yellow-400 fill-current"
                                : "text-gray-300"
                            )}
                          />
                        ))}
                      </div>
                      <p className="text-sm text-gray-600">
                        Based on {watch.reviewCount} reviews
                      </p>
                    </div>
                  </div>
                </div>

                {/* Sample Reviews */}
                <div className="space-y-6">
                  {[
                    {
                      id: 1,
                      author: 'James Wilson',
                      rating: 5,
                      date: '2024-01-15',
                      title: 'Exceptional timepiece',
                      content: 'This Submariner exceeded all my expectations. The build quality is outstanding, and the movement is incredibly precise. Worth every penny.'
                    },
                    {
                      id: 2,
                      author: 'Michael Chen',
                      rating: 5,
                      date: '2024-01-10',
                      title: 'Perfect diving companion',
                      content: 'Used this watch on multiple diving trips. The water resistance is excellent, and the bezel action is smooth and precise.'
                    }
                  ].map((review) => (
                    <div key={review.id} className="border-b border-gray-200 pb-6">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900">{review.author}</span>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={cn(
                                  "h-3 w-3",
                                  i < review.rating
                                    ? "text-yellow-400 fill-current"
                                    : "text-gray-300"
                                )}
                              />
                            ))}
                          </div>
                        </div>
                        <span className="text-sm text-gray-500">{review.date}</span>
                      </div>
                      <h4 className="font-medium text-gray-900 mb-2">{review.title}</h4>
                      <p className="text-gray-700">{review.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Related Products */}
        <div className="mt-16">
          <h2 className="font-luxury-serif text-2xl font-bold text-luxury-black mb-8">
            You Might Also Like
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {RELATED_WATCHES.map((relatedWatch) => (
              <WatchCard key={relatedWatch.id} watch={relatedWatch} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
