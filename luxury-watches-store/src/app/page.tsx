import { Metadata } from 'next'
import { HeroSection } from '@/components/sections/hero-section'
import { ProductShowcase, FeaturedProducts, NewArrivals } from '@/components/sections/product-showcase'
import { TrustBadges, CustomerTestimonials, CallToAction, Statistics } from '@/components/sections/conversion-elements'
import { BrandStory } from '@/components/sections/brand-story'
import { CategoryShowcase } from '@/components/home/<USER>'
import { NewsletterSignup } from '@/components/home/<USER>'
import { HeritageTimeline } from '@/components/home/<USER>'
import { SITE_CONFIG } from '@/lib/constants'
import { getAllProducts } from '@/lib/sanity/utils'
import { getBrandName, getCategoryName } from '@/lib/utils/index'

export const metadata: Metadata = {
  title: `${SITE_CONFIG.name} - Premium Watches & Fine Jewelry`,
  description: SITE_CONFIG.description,
  keywords: [
    'luxury watches',
    'fine jewelry',
    'atlas luxury',
    'premium timepieces',
    'swiss watches',
    'diamond jewelry',
    'rolex',
    'patek philippe',
    'cartier',
    'luxury accessories'
  ],
}

// Sample data for showcases
const featuredWatches = [
  {
    id: '1',
    name: 'Submariner Date',
    brand: 'Rolex',
    price: 9550,
    image: '/images/rolex-submariner.jpg',
    category: 'diving',
    rating: 4.9,
    isNew: true
  },
  {
    id: '2',
    name: 'Nautilus',
    brand: 'Patek Philippe',
    price: 85000,
    image: '/images/patek-nautilus.jpg',
    category: 'luxury',
    rating: 5.0
  },
  {
    id: '3',
    name: 'Speedmaster Professional',
    brand: 'Omega',
    price: 6350,
    image: '/images/omega-speedmaster.jpg',
    category: 'chronograph',
    rating: 4.8
  }
]

const featuredJewelry = [
  {
    id: '4',
    name: 'Love Ring',
    brand: 'Cartier',
    price: 1850,
    image: '/images/cartier-love-ring.jpg',
    category: 'rings',
    rating: 4.9,
    isNew: true
  },
  {
    id: '5',
    name: 'Alhambra Necklace',
    brand: 'Van Cleef & Arpels',
    price: 4200,
    image: '/images/vca-alhambra.jpg',
    category: 'necklaces',
    rating: 5.0
  },
  {
    id: '6',
    name: 'B.zero1 Bracelet',
    brand: 'Bulgari',
    price: 2950,
    image: '/images/bulgari-bzero1.jpg',
    category: 'bracelets',
    rating: 4.8
  }
]

export default async function HomePage() {
  // Get real data from Sanity
  const allProducts = await getAllProducts()

  // Separate watches and jewelry with proper category handling

  const watches = allProducts.filter(product => {
    const categoryName = getCategoryName(product.category).toLowerCase();
    return categoryName.includes('watch') ||
           ['diving', 'chronograph', 'luxury', 'sport', 'dress'].includes(categoryName);
  }).slice(0, 3);

  const jewelry = allProducts.filter(product => {
    const categoryName = getCategoryName(product.category).toLowerCase();
    return categoryName.includes('jewelry') ||
           ['rings', 'necklaces', 'bracelets', 'earrings'].includes(categoryName);
  }).slice(0, 3);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <HeroSection />

      {/* Trust Badges */}
      <TrustBadges />

      {/* Category Showcase */}
      <CategoryShowcase />

      {/* Featured Watches */}
      <ProductShowcase
        title="Exceptional Timepieces"
        subtitle="Discover our curated selection of the world's finest luxury watches"
        products={watches.length > 0 ? watches.map(w => ({
          id: w.id,
          name: w.name,
          brand: getBrandName(w.brand),
          price: w.price,
          image: w.images?.[0] || '/images/placeholder-watch.jpg',
          category: getCategoryName(w.category),
          rating: 4.8,
          isNew: w.isNew
        })) : featuredWatches}
        viewAllLink="/catalog?category=watches"
        type="watches"
      />

      {/* Statistics */}
      <Statistics />

      {/* Featured Jewelry */}
      <ProductShowcase
        title="Exquisite Jewelry"
        subtitle="Explore our collection of fine jewelry crafted with precious metals and gemstones"
        products={jewelry.length > 0 ? jewelry.map(j => ({
          id: j.id,
          name: j.name,
          brand: getBrandName(j.brand),
          price: j.price,
          image: j.images?.[0] || '/images/placeholder-jewelry.jpg',
          category: getCategoryName(j.category),
          rating: 4.9,
          isNew: j.isNew
        })) : featuredJewelry}
        viewAllLink="/catalog?category=jewelry"
        type="jewelry"
      />

      {/* Brand Story */}
      <BrandStory />

      {/* Customer Testimonials */}
      <CustomerTestimonials />

      {/* Heritage Timeline */}
      <HeritageTimeline />

      {/* Call to Action */}
      <CallToAction />

      {/* Newsletter Signup */}
      <NewsletterSignup />
    </div>
  )
}