import { <PERSON>adata } from 'next'
import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { ArrowLeft, Calendar, MapPin, Award, ExternalLink } from 'lucide-react'
import { LUXURY_BRANDS } from '@/lib/constants'
import { getAllProducts } from '@/lib/sanity/utils'
import { WatchCard } from '@/components/product/watch-card'
import { isBrandMatch } from '@/lib/utils/index'

interface BrandPageProps {
  params: Promise<{
    slug: string
  }>
}

// Extended brand data with detailed information
const brandDetails = {
  'rolex': {
    heroImage: 'https://images.unsplash.com/photo-1587836374828-4dbafa94cf0e?w=1200&h=600&fit=crop&crop=center',
    logo: 'https://images.unsplash.com/photo-1611930022073-b7a4ba5fcccd?w=200&h=100&fit=crop&crop=center',
    heritage: 'Founded in 1905 by <PERSON>, Rolex has become synonymous with precision, reliability, and prestige. The brand has consistently pushed the boundaries of watchmaking innovation.',
    keyCollections: ['Submariner', 'GMT-Master', 'Daytona', 'Datejust', 'Day-Date'],
    achievements: [
      'First waterproof wristwatch (Oyster, 1926)',
      'First self-winding mechanism with perpetual rotor (1931)',
      'First watch to display the date (1945)',
      'First watch to display day and date (1956)'
    ],
    website: 'https://rolex.com',
    country: 'Switzerland',
    headquarters: 'Geneva'
  },
  'patek-philippe': {
    heroImage: 'https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=1200&h=600&fit=crop&crop=center',
    logo: 'https://images.unsplash.com/photo-1611930022073-b7a4ba5fcccd?w=200&h=100&fit=crop&crop=center',
    heritage: 'Since 1839, Patek Philippe has been at the forefront of horological innovation. The family-owned company creates some of the most complicated and sought-after timepieces in the world.',
    keyCollections: ['Calatrava', 'Nautilus', 'Aquanaut', 'Complications', 'Grand Complications'],
    achievements: [
      'First Swiss wristwatch (1868)',
      'Most expensive watch ever sold at auction',
      'Over 100 patents in watchmaking',
      'Patek Philippe Seal certification'
    ],
    website: 'https://patek.com',
    country: 'Switzerland',
    headquarters: 'Geneva'
  },
  'audemars-piguet': {
    heroImage: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=1200&h=600&fit=crop&crop=center',
    logo: 'https://images.unsplash.com/photo-1611930022073-b7a4ba5fcccd?w=200&h=100&fit=crop&crop=center',
    heritage: 'Founded in 1875 by Jules Louis Audemars and Edward Auguste Piguet, the company has remained in the hands of the founding families. Known for the iconic Royal Oak design.',
    keyCollections: ['Royal Oak', 'Royal Oak Offshore', 'Millenary', 'Jules Audemars', 'Code 11.59'],
    achievements: [
      'First luxury sports watch (Royal Oak, 1972)',
      'Thinnest automatic movement',
      'Revolutionary octagonal bezel design',
      'Pioneering use of unconventional materials'
    ],
    website: 'https://audemarspiguet.com',
    country: 'Switzerland',
    headquarters: 'Le Brassus'
  },
  'omega': {
    heroImage: 'https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=1200&h=600&fit=crop&crop=center',
    logo: 'https://images.unsplash.com/photo-1611930022073-b7a4ba5fcccd?w=200&h=100&fit=crop&crop=center',
    heritage: 'Founded in 1848, Omega has been the official timekeeper of the Olympic Games and the choice of astronauts. The brand combines sporting heritage with luxury craftsmanship.',
    keyCollections: ['Speedmaster', 'Seamaster', 'Constellation', 'De Ville', 'Planet Ocean'],
    achievements: [
      'First watch on the moon (Speedmaster)',
      'Official timekeeper of Olympics since 1932',
      'Co-Axial escapement innovation',
      'Master Chronometer certification'
    ],
    website: 'https://omegawatches.com',
    country: 'Switzerland',
    headquarters: 'Biel/Bienne'
  },
  'cartier': {
    heroImage: 'https://images.unsplash.com/photo-1509048191080-d2e2678e67b4?w=1200&h=600&fit=crop&crop=center',
    logo: 'https://images.unsplash.com/photo-1611930022073-b7a4ba5fcccd?w=200&h=100&fit=crop&crop=center',
    heritage: 'Founded in 1847, Cartier is renowned for its jewelry and luxury watches. The "Jeweler of Kings and King of Jewelers" has created timepieces for royalty and celebrities.',
    keyCollections: ['Tank', 'Santos', 'Ballon Bleu', 'Panthère', 'Drive'],
    achievements: [
      'First modern wristwatch (Santos, 1904)',
      'Iconic Tank design (1917)',
      'Royal warrant holders',
      'Pioneering jewelry-watch combinations'
    ],
    website: 'https://cartier.com',
    country: 'France',
    headquarters: 'Paris'
  },
  'breitling': {
    heroImage: 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=1200&h=600&fit=crop&crop=center',
    logo: 'https://images.unsplash.com/photo-1611930022073-b7a4ba5fcccd?w=200&h=100&fit=crop&crop=center',
    heritage: 'Founded in 1884, Breitling specializes in aviation chronographs and instruments. The brand has been the choice of pilots and aviation professionals for over a century.',
    keyCollections: ['Navitimer', 'Superocean', 'Avenger', 'Premier', 'Chronomat'],
    achievements: [
      'First independent chronograph pushpiece (1915)',
      'Official supplier to aviation industry',
      'Chronometer certification for all models',
      'Emergency beacon technology'
    ],
    website: 'https://breitling.com',
    country: 'Switzerland',
    headquarters: 'Grenchen'
  }
}

export async function generateMetadata({ params }: BrandPageProps): Promise<Metadata> {
  const { slug } = await params
  const brand = LUXURY_BRANDS.find(b => b.slug === slug)
  
  if (!brand) {
    return {
      title: 'Brand Not Found - Luxury Watches Store'
    }
  }

  return {
    title: `${brand.name} Luxury Watches - Luxury Watches Store`,
    description: `Discover ${brand.name} luxury watches. ${brand.description}`,
  }
}

export default async function BrandPage({ params }: BrandPageProps) {
  const { slug } = await params
  const brand = LUXURY_BRANDS.find(b => b.slug === slug)
  const details = brandDetails[slug as keyof typeof brandDetails]
  
  if (!brand || !details) {
    notFound()
  }

  // Get products from this brand
  const allProducts = await getAllProducts()
  const brandProducts = allProducts.filter(product =>
    isBrandMatch(product.brand, brand.name)
  )

  return (
    <div className="min-h-screen bg-luxury-white">
      {/* Back Navigation */}
      <div className="container mx-auto px-4 pt-8">
        <Link
          href="/brands"
          className="inline-flex items-center gap-2 text-luxury-gold hover:text-luxury-gold-dark transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Brands
        </Link>
      </div>

      {/* Hero Section */}
      <section className="relative py-20">
        <div className="absolute inset-0">
          <Image
            src={details.heroImage}
            alt={`${brand.name} heritage`}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black/50" />
        </div>
        
        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="mb-6">
              <Image
                src={details.logo}
                alt={`${brand.name} logo`}
                width={200}
                height={100}
                className="mx-auto mb-6 bg-white/10 rounded-lg p-4"
              />
            </div>
            <h1 className="font-luxury-serif text-5xl md:text-6xl font-bold mb-6">
              {brand.name}
            </h1>
            <p className="text-xl leading-relaxed max-w-2xl mx-auto">
              {brand.description}
            </p>
          </div>
        </div>
      </section>

      {/* Brand Information */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            {/* Heritage */}
            <div>
              <h2 className="font-luxury-serif text-3xl font-bold text-luxury-black mb-6">
                Heritage & Legacy
              </h2>
              <p className="text-gray-700 leading-relaxed mb-6">
                {details.heritage}
              </p>
              
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-luxury-gold" />
                  <div>
                    <div className="text-sm text-gray-500">Founded</div>
                    <div className="font-semibold">{brand.founded}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-5 h-5 text-luxury-gold" />
                  <div>
                    <div className="text-sm text-gray-500">Headquarters</div>
                    <div className="font-semibold">{details.headquarters}</div>
                  </div>
                </div>
              </div>
              
              <a
                href={details.website}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-luxury-gold hover:text-luxury-gold-dark font-semibold"
              >
                Visit Official Website
                <ExternalLink className="w-4 h-4" />
              </a>
            </div>

            {/* Key Collections & Achievements */}
            <div>
              <h3 className="font-luxury-serif text-2xl font-bold text-luxury-black mb-4">
                Iconic Collections
              </h3>
              <div className="grid grid-cols-2 gap-2 mb-8">
                {details.keyCollections.map((collection) => (
                  <div
                    key={collection}
                    className="px-3 py-2 bg-luxury-cream text-luxury-black text-sm rounded-lg text-center"
                  >
                    {collection}
                  </div>
                ))}
              </div>

              <h3 className="font-luxury-serif text-2xl font-bold text-luxury-black mb-4">
                Key Achievements
              </h3>
              <ul className="space-y-3">
                {details.achievements.map((achievement, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <Award className="w-5 h-5 text-luxury-gold mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{achievement}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Brand Products */}
      {brandProducts.length > 0 && (
        <section className="py-20 bg-luxury-cream">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-4">
                {brand.name} Collection
              </h2>
              <p className="text-xl text-gray-600">
                Discover our curated selection of {brand.name} timepieces
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {brandProducts.slice(0, 6).map((product) => (
                <WatchCard key={product.id} watch={product} />
              ))}
            </div>
            
            {brandProducts.length > 6 && (
              <div className="text-center mt-12">
                <Link
                  href={`/catalog?brand=${brand.name}`}
                  className="inline-flex items-center gap-2 px-8 py-4 bg-luxury-gold text-luxury-black font-semibold rounded-lg hover:bg-luxury-gold-dark transition-colors"
                >
                  View All {brand.name} Watches
                  <ArrowLeft className="w-5 h-5 rotate-180" />
                </Link>
              </div>
            )}
          </div>
        </section>
      )}
    </div>
  )
}
