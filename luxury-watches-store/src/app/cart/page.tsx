'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Plus, Minus, Trash2, ShoppingBag, ArrowLeft, Truck, Shield, RotateCcw } from 'lucide-react';
import { useCart } from '@/contexts/cart-context';
import { Button } from '@/components/ui/button';
import { formatPrice, cn } from '@/lib/utils';

export default function CartPage() {
  const { 
    items, 
    total, 
    itemCount, 
    updateQuantity, 
    removeItem, 
    clearCart 
  } = useCart();

  const [promoCode, setPromoCode] = useState('');
  const [isPromoApplied, setIsPromoApplied] = useState(false);

  const shipping = total >= 1000 ? 0 : 50;
  const tax = total * 0.08; // 8% tax
  const discount = isPromoApplied ? total * 0.1 : 0; // 10% discount
  const finalTotal = total + shipping + tax - discount;

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId);
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  const handlePromoCode = () => {
    if (promoCode.toLowerCase() === 'luxury10') {
      setIsPromoApplied(true);
    }
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-luxury-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-md mx-auto text-center">
            <ShoppingBag className="h-24 w-24 text-gray-300 mx-auto mb-6" />
            <h1 className="font-luxury-serif text-3xl font-bold text-luxury-black mb-4">
              Your cart is empty
            </h1>
            <p className="text-gray-600 mb-8">
              Discover our collection of luxury timepieces and add some to your cart.
            </p>
            <Button asChild variant="luxury" size="lg">
              <Link href="/catalog">
                Continue Shopping
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-luxury-white">
      {/* Header */}
      <div className="bg-luxury-cream py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="ghost" size="icon" asChild>
              <Link href="/catalog">
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </Button>
            <h1 className="font-luxury-serif text-4xl md:text-5xl font-bold text-luxury-black">
              Shopping Cart
            </h1>
          </div>
          <p className="text-gray-600 text-lg">
            {itemCount} {itemCount === 1 ? 'item' : 'items'} in your cart
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-6">
            {items.map((item) => (
              <motion.div
                key={item.id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="bg-white rounded-lg shadow-luxury p-6"
              >
                <div className="flex gap-6">
                  {/* Product Image */}
                  <div className="relative w-32 h-32 flex-shrink-0">
                    <Image
                      src={item.watch.images?.[0] || 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop&crop=center'}
                      alt={`${typeof item.watch.brand === 'string' ? item.watch.brand : item.watch.brand?.name || 'Watch'} ${item.watch.name}`}
                      fill
                      className="object-cover rounded-lg"
                    />
                    {item.watch.isNew && (
                      <span className="absolute -top-2 -right-2 bg-luxury-gold text-luxury-black text-xs font-semibold px-2 py-1 rounded-full">
                        NEW
                      </span>
                    )}
                  </div>

                  {/* Product Details */}
                  <div className="flex-1">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <p className="text-luxury-gold text-sm font-medium uppercase tracking-wide">
                          {typeof item.watch.brand === 'string' ? item.watch.brand : item.watch.brand?.name || 'Unknown Brand'}
                        </p>
                        <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black">
                          {item.watch.name}
                        </h3>
                        <p className="text-gray-600 text-sm mt-1">
                          {item.watch.category}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeItem(item.id)}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <Trash2 className="h-5 w-5" />
                      </Button>
                    </div>

                    {/* Price and Quantity */}
                    <div className="flex items-center justify-between mt-4">
                      <div className="flex items-center gap-3">
                        {item.watch.originalPrice && (
                          <span className="text-gray-400 line-through">
                            {formatPrice(item.watch.originalPrice)}
                          </span>
                        )}
                        <span className="font-semibold text-luxury-gold text-lg">
                          {formatPrice(item.watch.price)}
                        </span>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center gap-3">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          className="h-10 w-10"
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="w-12 text-center font-medium text-lg">
                          {item.quantity}
                        </span>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          className="h-10 w-10"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Item Total */}
                    <div className="mt-4 text-right">
                      <span className="font-luxury-serif text-xl font-bold text-luxury-black">
                        {formatPrice((item.watch.price || 0) * item.quantity)}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}

            {/* Clear Cart */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200">
              <Button
                variant="ghost"
                onClick={clearCart}
                className="text-red-500 hover:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear Cart
              </Button>
              <Button variant="outline" asChild>
                <Link href="/catalog">
                  Continue Shopping
                </Link>
              </Button>
            </div>
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            {/* Summary Card */}
            <div className="bg-white rounded-lg shadow-luxury p-6">
              <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black mb-6">
                Order Summary
              </h3>

              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal ({itemCount} items)</span>
                  <span className="font-medium">{formatPrice(total)}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium">
                    {shipping === 0 ? (
                      <span className="text-green-600">Free</span>
                    ) : (
                      formatPrice(shipping)
                    )}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Tax</span>
                  <span className="font-medium">{formatPrice(tax)}</span>
                </div>

                {isPromoApplied && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount (LUXURY10)</span>
                    <span>-{formatPrice(discount)}</span>
                  </div>
                )}

                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between">
                    <span className="font-luxury-serif text-lg font-semibold">Total</span>
                    <span className="font-luxury-serif text-xl font-bold text-luxury-gold">
                      {formatPrice(finalTotal)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Promo Code */}
              {!isPromoApplied && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="Promo code"
                      value={promoCode}
                      onChange={(e) => setPromoCode(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-luxury-gold focus:border-transparent"
                    />
                    <Button onClick={handlePromoCode} variant="outline">
                      Apply
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Try code: LUXURY10 for 10% off
                  </p>
                </div>
              )}

              {/* Checkout Button */}
              <Button className="w-full mt-6" variant="luxury" size="lg" asChild>
                <Link href="/checkout">
                  Proceed to Checkout
                </Link>
              </Button>
            </div>

            {/* Service Features */}
            <div className="bg-luxury-cream rounded-lg p-6">
              <h4 className="font-semibold text-luxury-black mb-4">Why Shop With Us</h4>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Truck className="h-5 w-5 text-luxury-gold" />
                  <span className="text-sm text-gray-700">Free shipping over $1,000</span>
                </div>
                <div className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-luxury-gold" />
                  <span className="text-sm text-gray-700">5-year warranty</span>
                </div>
                <div className="flex items-center gap-3">
                  <RotateCcw className="h-5 w-5 text-luxury-gold" />
                  <span className="text-sm text-gray-700">30-day returns</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
