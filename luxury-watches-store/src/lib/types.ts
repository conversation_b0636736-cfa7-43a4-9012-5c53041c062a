export interface Watch {
  id: string;
  name: string;
  brand: string;
  price: number;
  originalPrice?: number | null;
  priceOnRequest?: boolean;
  image: string;
  images: WatchImage[];
  category: string;
  slug: string;
  description: string;
  shortDescription: string;
  features: string[];
  specifications: WatchSpecifications;
  inStock: boolean;
  availability: 'in-stock' | 'out-of-stock' | 'pre-order';
  isNew: boolean;
  isBestseller: boolean;
  featured: boolean;
  rating?: number;
  reviewCount?: number;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface WatchImage {
  id: string;
  url: string;
  alt: string;
  isPrimary: boolean;
  order: number;
}

export interface WatchSpecifications {
  caseSize: string;
  caseMaterial: string;
  caseThickness?: string;
  dialColor: string;
  movement: string;
  powerReserve?: string;
  waterResistance?: string;
  crystal: string;
  braceletMaterial?: string;
  functions: string[];
  warranty?: string;
}

export interface Brand {
  id: string;
  name: string;
  slug: string;
  description: string;
  founded: number;
  country: string;
  logo?: string;
  website?: string;
  featured: boolean;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  image?: string;
  parentId?: string;
  featured: boolean;
}

export interface Collection {
  id: string;
  name: string;
  slug: string;
  description: string;
  image?: string;
  brandId: string;
  featured: boolean;
  watches: Watch[];
}

export interface CartItem {
  id: string;
  watchId: string;
  watch: SimpleWatch;
  quantity: number;
  addedAt: string;
}

export interface WishlistItem {
  id: string;
  watchId: string;
  watch: SimpleWatch;
  addedAt: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  addresses: Address[];
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  id: string;
  type: 'billing' | 'shipping';
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
}

export interface UserPreferences {
  currency: string;
  language: string;
  newsletter: boolean;
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
}

export interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  status: OrderStatus;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  currency: string;
  shippingAddress: Address;
  billingAddress: Address;
  paymentMethod: PaymentMethod;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  watchId: string;
  watch: Watch;
  quantity: number;
  price: number;
  total: number;
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'cancelled'
  | 'refunded';

export interface PaymentMethod {
  type: 'credit_card' | 'paypal' | 'bank_transfer' | 'crypto';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
}

export interface Review {
  id: string;
  watchId: string;
  userId: string;
  user: {
    firstName: string;
    lastName: string;
    avatar?: string;
  };
  rating: number;
  title: string;
  content: string;
  verified: boolean;
  helpful: number;
  createdAt: string;
  updatedAt: string;
}

export interface SearchFilters {
  query?: string;
  brands?: string[];
  categories?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  availability?: ('in-stock' | 'out-of-stock' | 'pre-order')[];
  features?: string[];
  sortBy?: 'featured' | 'price-asc' | 'price-desc' | 'newest' | 'rating' | 'brand-asc' | 'brand-desc';
  page?: number;
  limit?: number;
}

export interface SearchResult {
  watches: Watch[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  filters: {
    brands: { name: string; count: number }[];
    categories: { name: string; count: number }[];
    priceRange: { min: number; max: number };
    features: { name: string; count: number }[];
  };
}

// Product interface for catalog
export interface Product {
  id: string;
  name: string;
  slug?: { current: string };
  brand: string | { name: string; slug?: { current: string }; description?: string; logo?: SanityImageAsset };
  model?: string;
  price: number;
  originalPrice?: number | null;
  images: string[];
  category: string;
  collection?: string;
  description: string;
  features: string[];
  specifications: {
    caseSize: string;
    caseMaterial: string;
    movement: string;
    waterResistance?: string;
    powerReserve?: string;
    crystal: string;
  };
  inStock: boolean;
  isNew: boolean;
  isBestseller: boolean;
  rating?: number;
  reviewCount?: number;
}

// Simple watch interface for catalog and cart
export interface SimpleWatch {
  id: string;
  name: string;
  slug?: { current: string };
  brand: string | { name: string; slug?: { current: string }; description?: string; logo?: SanityImageAsset };
  price: number | null;
  originalPrice?: number | null;
  images: string[];
  category: string;
  collection?: string;
  description: string;
  features: string[];
  inStock: boolean;
  isNew: boolean;
  isBestseller: boolean;
}

// Authentication types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  isEmailVerified: boolean;
  createdAt: string;
}

export interface AuthState {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Blog Post types
export interface BlogPost {
  _id: string;
  title: string;
  slug: { current: string };
  excerpt?: string;
  content?: PortableTextBlock[];
  featuredImage?: SanityImageAsset;
  author?: string;
  publishedAt: string;
  categories?: string[];
  tags?: string[];
  seo?: SEOData;
}

export interface SEOData {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: SanityImageAsset;
}

export interface SanityImageAsset {
  asset?: {
    url: string;
    _id: string;
  };
  alt?: string;
  hotspot?: {
    x: number;
    y: number;
  };
  crop?: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

export interface PortableTextBlock {
  _type: string;
  style?: string;
  children?: PortableTextSpan[];
  markDefs?: PortableTextMarkDef[];
  level?: number;
  listItem?: string;
}

export interface PortableTextSpan {
  _type: 'span';
  text: string;
  marks?: string[];
}

export interface PortableTextMarkDef {
  _type: string;
  _key: string;
  [key: string]: unknown;
}

// Detailed Watch interface for product pages
export interface DetailedWatch extends Watch {
  relatedProducts?: Watch[];
  reviews?: Review[];
  variants?: WatchVariant[];
}

export interface WatchVariant {
  id: string;
  name: string;
  price: number;
  images: WatchImage[];
  specifications: WatchSpecifications;
  inStock: boolean;
}
