import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { SanityImageAsset } from '@/lib/types';

// Utility function for combining Tailwind classes
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Brand utility types and functions
export type BrandType = string | {
  name: string;
  slug?: { current: string };
  description?: string;
  logo?: SanityImageAsset
};

// Get brand name from brand object or string
export function getBrandName(brand: BrandType): string {
  if (typeof brand === 'string') {
    return brand;
  }
  return brand.name;
}

// Get brand slug from brand object or string
export function getBrandSlug(brand: BrandType): string {
  if (typeof brand === 'string') {
    return brand.toLowerCase().replace(/\s+/g, '-');
  }
  return brand.slug?.current || brand.name.toLowerCase().replace(/\s+/g, '-');
}

// Check if brand matches a given name (case-insensitive)
export function isBrandMatch(brand: BrandType, targetName: string): boolean {
  const brandName = getBrandName(brand);
  return brandName.toLowerCase() === targetName.toLowerCase();
}

// Get category name from category object or string
export function getCategoryName(category: any): string {
  if (typeof category === 'string') return category;
  if (category && typeof category === 'object' && category.name) return category.name;
  return '';
}

// Convert brand to display format
export function formatBrandForDisplay(brand: BrandType): string {
  return getBrandName(brand);
}

// Format currency
export function formatCurrency(
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
}

// Format price with "Price on Request" option
export function formatPrice(price: number, priceOnRequest?: boolean): string {
  if (priceOnRequest) {
    return 'Price on Request';
  }
  return formatCurrency(price);
}

// Generate slug from string
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

// Truncate text
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).trim() + '...';
}

// Format date
export function formatDate(
  date: string | Date,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', options);
}

// Calculate reading time
export function calculateReadingTime(text: string): number {
  const wordsPerMinute = 200;
  const words = text.trim().split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
}

// Validate email
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate phone number (basic)
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
}

// Generate random ID
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

// Debounce function
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle function
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Deep clone object
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
  if (typeof obj === 'object') {
    const clonedObj = {} as Record<string, unknown>;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone((obj as Record<string, unknown>)[key]);
      }
    }
    return clonedObj as T;
  }
  return obj;
}

// Get image URL with fallback
export function getImageUrl(url?: string, fallback: string = '/placeholder-watch.jpg'): string {
  return url || fallback;
}

// Format watch specifications for display
export function formatSpecifications(specs: Record<string, unknown>): Array<{ label: string; value: string }> {
  const formatMap: Record<string, string> = {
    movement: 'Movement',
    caseSize: 'Case Size',
    caseMaterial: 'Case Material',
    dialColor: 'Dial Color',
    strapMaterial: 'Strap Material',
    waterResistance: 'Water Resistance',
    powerReserve: 'Power Reserve',
    crystalType: 'Crystal Type',
    weight: 'Weight',
  };

  return Object.entries(specs)
    .filter(([, value]) => value !== undefined && value !== null && value !== '')
    .map(([key, value]) => ({
      label: formatMap[key] || key.charAt(0).toUpperCase() + key.slice(1),
      value: Array.isArray(value) ? value.join(', ') : String(value),
    }));
}

// Calculate discount percentage
export function calculateDiscount(originalPrice: number, salePrice: number): number {
  return Math.round(((originalPrice - salePrice) / originalPrice) * 100);
}

// Check if item is in stock
export function isInStock(availability: string): boolean {
  return availability === 'in-stock';
}

// Format availability status
export function formatAvailability(availability: string): string {
  const statusMap: Record<string, string> = {
    'in-stock': 'In Stock',
    'pre-order': 'Pre-Order',
    'out-of-stock': 'Out of Stock',
  };
  return statusMap[availability] || availability;
}

// Get availability color
export function getAvailabilityColor(availability: string): string {
  const colorMap: Record<string, string> = {
    'in-stock': 'text-green-600',
    'pre-order': 'text-yellow-600',
    'out-of-stock': 'text-red-600',
  };
  return colorMap[availability] || 'text-gray-600';
}

// Local storage helpers
export const storage = {
  get: <T>(key: string): T | null => {
    if (typeof window === 'undefined') return null;
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch {
      return null;
    }
  },
  set: <T>(key: string, value: T): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch {
      // Handle storage errors silently
    }
  },
  remove: (key: string): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem(key);
    } catch {
      // Handle storage errors silently
    }
  },
};
