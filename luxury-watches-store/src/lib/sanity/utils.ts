import { client, queries, urlFor } from './client'
import { productAPI, brandAPI, categoryAPI, collectionAPI, blogAPI, reviewAPI, siteAPI } from './api'
import { SimpleWatch, BlogPost, SanityImageAsset } from '@/lib/types'
import { getCategoryName } from '../utils/index'

// Temporary mock data for demo
const DEMO_PRODUCTS: SimpleWatch[] = [
  {
    id: '1',
    name: 'Submariner Date',
    brand: 'Rolex',
    price: 12500,
    originalPrice: 13000,
    images: ['https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=400&h=400&fit=crop'],
    category: 'Sports',
    description: 'The Rolex Submariner Date is a legendary diving watch with exceptional water resistance.',
    features: ['Waterproof to 300m', 'Automatic movement', 'Ceramic bezel'],
    isNew: true,
    isBestseller: true,
    inStock: true
  },
  {
    id: '2',
    name: 'Speedmaster Professional',
    brand: 'Omega',
    price: 6500,
    images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop'],
    category: 'Sports',
    description: 'The legendary Moonwatch worn by astronauts during the Apollo missions.',
    features: ['Manual winding', 'Chronograph', 'Hesalite crystal'],
    isNew: false,
    isBestseller: true,
    inStock: true
  },
  {
    id: '3',
    name: 'Nautilus 5711',
    brand: 'Patek Philippe',
    price: 85000,
    images: ['https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=400&h=400&fit=crop'],
    category: 'Luxury',
    description: 'An iconic luxury sports watch with distinctive porthole design.',
    features: ['Automatic movement', 'Date display', 'Water resistant'],
    isNew: false,
    isBestseller: true,
    inStock: false
  },
  {
    id: '4',
    name: 'Royal Oak',
    brand: 'Audemars Piguet',
    price: 32000,
    images: ['https://images.unsplash.com/photo-1509048191080-d2e2678e67b4?w=400&h=400&fit=crop'],
    category: 'Luxury',
    description: 'The revolutionary luxury sports watch that changed the industry.',
    features: ['Octagonal bezel', 'Tapisserie dial', 'Integrated bracelet'],
    isNew: false,
    isBestseller: false,
    inStock: true
  },
  {
    id: '5',
    name: 'Daytona',
    brand: 'Rolex',
    price: 28000,
    images: ['https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400&h=400&fit=crop'],
    category: 'Sports',
    description: 'The ultimate racing chronograph for professional drivers.',
    features: ['Chronograph', 'Tachymeter bezel', 'Automatic movement'],
    isNew: true,
    isBestseller: true,
    inStock: true
  },
  {
    id: '6',
    name: 'Seamaster Planet Ocean',
    brand: 'Omega',
    price: 7200,
    images: ['https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=400&h=400&fit=crop'],
    category: 'Sports',
    description: 'Professional diving watch with exceptional water resistance.',
    features: ['600m water resistance', 'Helium escape valve', 'Co-Axial movement'],
    isNew: false,
    isBestseller: false,
    inStock: true
  }
];

// Product functions using new API
export async function getAllProducts(language = 'en'): Promise<SimpleWatch[]> {
  try {
    const products = await productAPI.getAll({ language })
    if (!products || !Array.isArray(products) || products.length === 0) {
      // Return demo products if no real data
      return DEMO_PRODUCTS
    }
    return products.map(transformProductToSimpleWatch)
  } catch (error) {
    console.error('Error fetching products:', error)
    // Return demo products on error
    return DEMO_PRODUCTS
  }
}

// Get featured products - updated to use new API
export async function getFeaturedProducts(limit = 6, language = 'en'): Promise<SimpleWatch[]> {
  try {
    const products = await productAPI.getFeatured(limit, language)
    if (!products || !Array.isArray(products)) {
      return []
    }
    return products.map(transformProductToSimpleWatch)
  } catch (error) {
    console.error('Error fetching featured products:', error)
    return []
  }
}

export async function getProductById(id: string): Promise<SimpleWatch | null> {
  try {
    // Try to fetch by ID first
    const [productsById, jewelryById] = await Promise.all([
      client.fetch(queries.productById, { id }),
      client.fetch(queries.jewelryById, { id })
    ])

    let product = productsById || jewelryById

    // If not found by ID, try by slug
    if (!product) {
      const [productsBySlug, jewelryBySlug] = await Promise.all([
        client.fetch(queries.productBySlug, { slug: id }),
        client.fetch(`*[_type == "jewelry" && slug.current == $slug][0] {
          _id, name, slug, "brand": brand->name, "category": category->name,
          images, price, originalPrice, currency, description, specifications,
          stock, isAvailable, isFeatured, tags, _createdAt
        }`, { slug: id })
      ])
      product = productsBySlug || jewelryBySlug
    }

    if (product) {
      return transformProductToSimpleWatch(product)
    }

    return null
  } catch (error) {
    console.error('Error fetching product by ID/slug:', error)
    return null
  }
}

// Brand functions - updated to use new API
export async function getAllBrands(language = 'en') {
  try {
    const brands = await brandAPI.getAll(language)
    if (!brands || !Array.isArray(brands)) {
      return []
    }
    return brands
  } catch (error) {
    console.error('Error fetching brands:', error)
    return []
  }
}

export async function getBrandBySlug(slug: string, language = 'en') {
  try {
    return await brandAPI.getBySlug(slug, language)
  } catch (error) {
    console.error('Error fetching brand by slug:', error)
    return null
  }
}

// Category functions - updated to use new API
export async function getAllCategories(language = 'en') {
  try {
    const categories = await categoryAPI.getAll(language)
    if (!categories || !Array.isArray(categories)) {
      return []
    }
    return categories
  } catch (error) {
    console.error('Error fetching categories:', error)
    return []
  }
}

// Collection functions - updated to use new API
export async function getAllCollections(language = 'en') {
  try {
    const collections = await collectionAPI.getAll(language)
    return collections || []
  } catch (error) {
    console.error('Error fetching collections:', error)
    return []
  }
}

// Review functions - updated to use new API
export async function getProductReviews(productId: string, start = 0, limit = 10) {
  try {
    return await reviewAPI.getByProduct(productId, { start, end: start + limit })
  } catch (error) {
    console.error('Error fetching product reviews:', error)
    return []
  }
}

// Blog functions - updated to use new API
export async function getAllBlogPosts(language = 'en', start = 0, limit = 10): Promise<BlogPost[]> {
  try {
    const posts = await blogAPI.getAll({ start, end: start + limit, language })
    if (!posts || !Array.isArray(posts)) {
      return []
    }
    return posts
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return []
  }
}

export async function getBlogPostBySlug(slug: string, language = 'en'): Promise<BlogPost | null> {
  try {
    const post = await blogAPI.getBySlug(slug, language)
    if (!post || typeof post !== 'object' || !('_id' in post)) {
      return null
    }
    return post as BlogPost
  } catch (error) {
    console.error('Error fetching blog post by slug:', error)
    return null
  }
}

// Site settings - updated to use new API
export async function getSiteSettings() {
  try {
    return await siteAPI.getSettings()
  } catch (error) {
    console.error('Error fetching site settings:', error)
    return null
  }
}

// Transform functions
interface SanityProduct {
  _id: string;
  name: string;
  brand: string | { name: string; slug?: { current: string }; description?: string; logo?: SanityImageAsset };
  pricing?: {
    basePrice?: number;
    originalPrice?: number;
    priceOnRequest?: boolean;
  };
  images?: SanityImageAsset[];
  category: string | { name: string; slug?: { current: string } };
  description?: string;
  features?: string[];
  isNew?: boolean;
  isBestseller?: boolean;
  isAvailable?: boolean;
}

function transformProductToSimpleWatch(product: SanityProduct): SimpleWatch {
  // Extract pricing information with proper fallbacks
  const basePrice = product.pricing?.basePrice;
  const originalPrice = product.pricing?.originalPrice;
  const priceOnRequest = product.pricing?.priceOnRequest;

  // Extract category name from object or use string directly

  return {
    id: product._id,
    name: product.name,
    brand: product.brand,
    price: priceOnRequest ? null : basePrice || null,
    originalPrice: originalPrice || null,
    images: product.images?.map((img) => urlFor(img).width(400).height(400).url()) || ['/images/placeholder-watch.svg'],
    category: getCategoryName(product.category),
    description: product.description || '',
    features: product.features || [],
    isNew: product.isNew || false,
    isBestseller: product.isBestseller || false,
    inStock: product.isAvailable || false
  }
}

// Image URL helper
export function getImageUrl(image: SanityImageAsset | null | undefined, width = 400, height = 400): string {
  if (!image) return '/images/placeholder-watch.svg'

  // Handle Sanity images
  try {
    return urlFor(image).width(width).height(height).url()
  } catch (error) {
    return '/images/placeholder-watch.jpg'
  }
}


