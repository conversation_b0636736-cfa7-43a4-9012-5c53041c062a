/**
 * Sanity API functions with optimized queries and caching
 * Centralized data fetching with error handling and fallbacks
 */

import { client, serverClient } from './client'
import {
  PRODUCT_QUERIES,
  BRAND_QUERIES,
  CATEGORY_QUERIES,
  COLLECTION_QUERIES,
  BL<PERSON>G_QUERIES,
  LANDING_PAGE_QUERIES,
  SITE_QUERIES,
  REVIEW_QUERIES
} from '../queries'

// Types
export interface QueryParams {
  [key: string]: any
}

export interface PaginationParams {
  start?: number
  end?: number
  limit?: number
}

export interface LanguageParams {
  language?: string
}

// Default parameters
const DEFAULT_LANGUAGE = 'en'
const DEFAULT_LIMIT = 12
const DEFAULT_START = 0

// Helper function to execute queries with error handling
async function executeQuery<T>(
  query: string, 
  params: QueryParams = {}, 
  useServerClient = false
): Promise<T | null> {
  try {
    const sanityClient = useServerClient ? serverClient : client
    const result = await sanityClient.fetch<T>(query, params)
    return result
  } catch (error) {
    console.error('Sanity query error:', error)
    return null
  }
}

// Product API functions
export const productAPI = {
  /**
   * Get all products with pagination
   */
  async getAll(params: PaginationParams & LanguageParams = {}) {
    const { start = DEFAULT_START, end = start + DEFAULT_LIMIT, language = DEFAULT_LANGUAGE } = params
    return executeQuery(PRODUCT_QUERIES.all, { start, end, language })
  },

  /**
   * Get product by slug
   */
  async getBySlug(slug: string, language = DEFAULT_LANGUAGE) {
    return executeQuery(PRODUCT_QUERIES.bySlug, { slug, language })
  },

  /**
   * Get featured products
   */
  async getFeatured(limit = 6, language = DEFAULT_LANGUAGE) {
    return executeQuery(PRODUCT_QUERIES.featured, { limit, language })
  },

  /**
   * Get new arrivals
   */
  async getNewArrivals(limit = 6, language = DEFAULT_LANGUAGE) {
    return executeQuery(PRODUCT_QUERIES.newArrivals, { limit, language })
  },

  /**
   * Get products by category
   */
  async getByCategory(categoryId: string, params: PaginationParams & LanguageParams = {}) {
    const { start = DEFAULT_START, end = start + DEFAULT_LIMIT, language = DEFAULT_LANGUAGE } = params
    return executeQuery(PRODUCT_QUERIES.byCategory, { categoryId, start, end, language })
  },

  /**
   * Get products by brand
   */
  async getByBrand(brandId: string, params: PaginationParams & LanguageParams = {}) {
    const { start = DEFAULT_START, end = start + DEFAULT_LIMIT, language = DEFAULT_LANGUAGE } = params
    return executeQuery(PRODUCT_QUERIES.byBrand, { brandId, start, end, language })
  },

  /**
   * Get products by collection
   */
  async getByCollection(collectionId: string, params: PaginationParams & LanguageParams = {}) {
    const { start = DEFAULT_START, end = start + DEFAULT_LIMIT, language = DEFAULT_LANGUAGE } = params
    return executeQuery(PRODUCT_QUERIES.byCollection, { collectionId, start, end, language })
  },

  /**
   * Search products
   */
  async search(searchTerm: string, limit = DEFAULT_LIMIT, language = DEFAULT_LANGUAGE) {
    return executeQuery(PRODUCT_QUERIES.search, { searchTerm, limit, language })
  },

  /**
   * Get related products
   */
  async getRelated(productId: string, categoryId?: string, brandId?: string, collectionId?: string, limit = 4, language = DEFAULT_LANGUAGE) {
    return executeQuery(PRODUCT_QUERIES.related, { 
      productId, 
      categoryId, 
      brandId, 
      collectionId, 
      limit, 
      language 
    })
  },

  /**
   * Get product slugs for static generation
   */
  async getSlugs() {
    return executeQuery(PRODUCT_QUERIES.slugs, {}, true)
  }
}

// Brand API functions
export const brandAPI = {
  async getAll(language = DEFAULT_LANGUAGE) {
    return executeQuery(BRAND_QUERIES.all, { language })
  },

  async getBySlug(slug: string, language = DEFAULT_LANGUAGE) {
    return executeQuery(BRAND_QUERIES.bySlug, { slug, language })
  },

  async getSlugs() {
    return executeQuery(BRAND_QUERIES.slugs, {}, true)
  }
}

// Category API functions
export const categoryAPI = {
  async getAll(language = DEFAULT_LANGUAGE) {
    return executeQuery(CATEGORY_QUERIES.all, { language })
  },

  async getBySlug(slug: string, language = DEFAULT_LANGUAGE) {
    return executeQuery(CATEGORY_QUERIES.bySlug, { slug, language })
  },

  async getSlugs() {
    return executeQuery(CATEGORY_QUERIES.slugs, {}, true)
  }
}

// Collection API functions
export const collectionAPI = {
  async getAll(language = DEFAULT_LANGUAGE) {
    return executeQuery(COLLECTION_QUERIES.all, { language })
  },

  async getFeatured(limit = 6, language = DEFAULT_LANGUAGE) {
    return executeQuery(COLLECTION_QUERIES.featured, { limit, language })
  },

  async getBySlug(slug: string, language = DEFAULT_LANGUAGE) {
    return executeQuery(COLLECTION_QUERIES.bySlug, { slug, language })
  },

  async getSlugs() {
    return executeQuery(COLLECTION_QUERIES.slugs, {}, true)
  }
}

// Blog API functions
export const blogAPI = {
  async getAll(params: PaginationParams & LanguageParams = {}) {
    const { start = DEFAULT_START, end = start + DEFAULT_LIMIT, language = DEFAULT_LANGUAGE } = params
    return executeQuery(BLOG_QUERIES.all, { start, end, language })
  },

  async getFeatured(limit = 3, language = DEFAULT_LANGUAGE) {
    return executeQuery(BLOG_QUERIES.featured, { limit, language })
  },

  async getBySlug(slug: string, language = DEFAULT_LANGUAGE) {
    return executeQuery(BLOG_QUERIES.bySlug, { slug, language })
  },

  async getByCategory(category: string, params: PaginationParams & LanguageParams = {}) {
    const { start = DEFAULT_START, end = start + DEFAULT_LIMIT, language = DEFAULT_LANGUAGE } = params
    return executeQuery(BLOG_QUERIES.byCategory, { category, start, end, language })
  },

  async getSlugs() {
    return executeQuery(BLOG_QUERIES.slugs, {}, true)
  }
}

// Landing page API functions
export const landingPageAPI = {
  async getBySlug(slug: string, language = DEFAULT_LANGUAGE) {
    return executeQuery(LANDING_PAGE_QUERIES.bySlug, { slug, language })
  },

  async getSlugs() {
    return executeQuery(LANDING_PAGE_QUERIES.slugs, {}, true)
  }
}

// Site settings API
export const siteAPI = {
  async getSettings() {
    return executeQuery(SITE_QUERIES.settings, {}, true)
  }
}

// Review API functions
export const reviewAPI = {
  async getByProduct(productId: string, params: PaginationParams = {}) {
    const { start = DEFAULT_START, end = start + DEFAULT_LIMIT } = params
    return executeQuery(REVIEW_QUERIES.byProduct, { productId, start, end })
  },

  async getFeatured(limit = 6) {
    return executeQuery(REVIEW_QUERIES.featured, { limit })
  }
}

// Utility functions for static generation
export const staticAPI = {
  /**
   * Get all slugs for static generation
   */
  async getAllSlugs() {
    const [products, brands, categories, collections, blogs, landingPages] = await Promise.all([
      productAPI.getSlugs(),
      brandAPI.getSlugs(),
      categoryAPI.getSlugs(),
      collectionAPI.getSlugs(),
      blogAPI.getSlugs(),
      landingPageAPI.getSlugs()
    ])

    return {
      products: products || [],
      brands: brands || [],
      categories: categories || [],
      collections: collections || [],
      blogs: blogs || [],
      landingPages: landingPages || []
    }
  },

  /**
   * Get paths for generateStaticParams
   */
  async getStaticPaths(type: 'products' | 'brands' | 'categories' | 'collections' | 'blogs' | 'landingPages') {
    const slugs = await staticAPI.getAllSlugs()
    const typeData = slugs[type]
    if (!Array.isArray(typeData)) {
      return []
    }
    return typeData.map((item: any) => ({
      slug: item.slug,
      locale: item.language || DEFAULT_LANGUAGE
    }))
  }
}

// Cache utilities (for future implementation)
export const cacheAPI = {
  /**
   * Cache key generator
   */
  generateKey(type: string, params: QueryParams = {}) {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key]
        return result
      }, {} as QueryParams)
    
    return `${type}:${JSON.stringify(sortedParams)}`
  },

  /**
   * Get cache TTL based on content type
   */
  getTTL(type: string) {
    const ttlMap = {
      products: 300, // 5 minutes
      brands: 3600, // 1 hour
      categories: 3600, // 1 hour
      collections: 1800, // 30 minutes
      blogs: 600, // 10 minutes
      settings: 3600 // 1 hour
    }
    return ttlMap[type as keyof typeof ttlMap] || 300
  }
}
