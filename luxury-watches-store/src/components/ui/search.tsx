'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { Search as SearchIcon, Clock, X, TrendingUp } from 'lucide-react';
import { getAllProducts } from '@/lib/sanity/utils';
import { SimpleWatch } from '@/lib/types';
import { debounce, formatPrice } from '@/lib/utils';
import { getBrandName } from '@/lib/utils/index';
import { Button } from './button';

interface SearchProps {
  isOpen: boolean;
  onClose: () => void;
  placeholder?: string;
  showHistory?: boolean;
}

interface SearchResult extends SimpleWatch {
  matchType: 'name' | 'brand' | 'description';
}

const SEARCH_HISTORY_KEY = 'luxury-watches-search-history';
const MAX_HISTORY_ITEMS = 5;
const MAX_RESULTS = 8;

export function Search({ isOpen, onClose, placeholder = "Search luxury watches...", showHistory = true }: SearchProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Load search history from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined' && showHistory) {
      const history = localStorage.getItem(SEARCH_HISTORY_KEY);
      if (history) {
        setSearchHistory(JSON.parse(history));
      }
    }
  }, [showHistory]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Search function with debounce
  const performSearch = useCallback(
    debounce(async (searchQuery: unknown) => {
      const query = searchQuery as string;
      if (!query.trim()) {
        setResults([]);
        setIsLoading(false);
        return;
      }

      try {
        const products = await getAllProducts();
        const searchResults: SearchResult[] = [];

        products.forEach(product => {
          const lowerQuery = query.toLowerCase();
          const lowerName = product.name.toLowerCase();
          const lowerBrand = (typeof product.brand === 'string' ? product.brand : product.brand?.name || '').toLowerCase();
          const lowerDescription = product.description.toLowerCase();

          if (lowerName.includes(lowerQuery)) {
            searchResults.push({ ...product, matchType: 'name' });
          } else if (lowerBrand.includes(lowerQuery)) {
            searchResults.push({ ...product, matchType: 'brand' });
          } else if (lowerDescription.includes(lowerQuery)) {
            searchResults.push({ ...product, matchType: 'description' });
          }
        });

        // Sort by relevance (name matches first, then brand, then description)
        searchResults.sort((a, b) => {
          const order = { name: 0, brand: 1, description: 2 };
          return order[a.matchType] - order[b.matchType];
        });

        setResults(searchResults.slice(0, MAX_RESULTS));
      } catch (error) {
        console.error('Search error:', error);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    }, 300),
    []
  );

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setSelectedIndex(-1);
    
    if (value.trim()) {
      setIsLoading(true);
      performSearch(value);
    } else {
      setResults([]);
      setIsLoading(false);
    }
  };

  // Handle search submission
  const handleSearch = (searchQuery: string) => {
    if (!searchQuery.trim()) return;

    // Add to search history
    if (showHistory) {
      const newHistory = [searchQuery, ...searchHistory.filter(item => item !== searchQuery)]
        .slice(0, MAX_HISTORY_ITEMS);
      setSearchHistory(newHistory);
      localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory));
    }

    // Navigate to catalog with search query
    router.push(`/catalog?search=${encodeURIComponent(searchQuery)}`);
    onClose();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter') {
      if (selectedIndex >= 0 && results[selectedIndex]) {
        router.push(`/catalog/${results[selectedIndex].id}`);
        onClose();
      } else {
        handleSearch(query);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < results.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev > -1 ? prev - 1 : -1);
    }
  };

  // Clear search history
  const clearHistory = () => {
    setSearchHistory([]);
    localStorage.removeItem(SEARCH_HISTORY_KEY);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="container mx-auto px-4 pt-20"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="max-w-2xl mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden">
            {/* Search Input */}
            <div className="relative p-6 border-b border-gray-100">
              <div className="relative">
                <SearchIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  ref={inputRef}
                  type="text"
                  value={query}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  placeholder={placeholder}
                  className="w-full pl-12 pr-12 py-4 text-lg bg-luxury-cream rounded-xl border-0 focus:outline-none focus:ring-2 focus:ring-luxury-gold"
                />
                {query && (
                  <button
                    onClick={() => {
                      setQuery('');
                      setResults([]);
                      inputRef.current?.focus();
                    }}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-5 w-5" />
                  </button>
                )}
              </div>
            </div>

            {/* Search Results */}
            <div className="max-h-96 overflow-y-auto">
              {isLoading && (
                <div className="p-6 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-luxury-gold mx-auto"></div>
                  <p className="text-gray-500 mt-2">Searching...</p>
                </div>
              )}

              {!isLoading && query && results.length > 0 && (
                <div className="p-2">
                  {results.map((product, index) => (
                    <Link
                      key={product.id}
                      href={`/catalog/${product.id}`}
                      onClick={onClose}
                      className={`flex items-center p-4 rounded-lg hover:bg-luxury-cream transition-colors ${
                        selectedIndex === index ? 'bg-luxury-cream' : ''
                      }`}
                    >
                      <div className="w-12 h-12 relative rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={product.images[0]}
                          alt={product.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="ml-4 flex-1">
                        <h4 className="font-medium text-luxury-black">{product.name}</h4>
                        <p className="text-sm text-gray-500">{getBrandName(product.brand)}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-luxury-gold">{formatPrice(product.price)}</p>
                        <p className="text-xs text-gray-400 capitalize">{product.matchType} match</p>
                      </div>
                    </Link>
                  ))}
                  
                  {results.length === MAX_RESULTS && (
                    <div className="p-4 text-center border-t border-gray-100">
                      <Button
                        variant="outline"
                        onClick={() => handleSearch(query)}
                        className="text-sm"
                      >
                        View all results for "{query}"
                      </Button>
                    </div>
                  )}
                </div>
              )}

              {!isLoading && query && results.length === 0 && (
                <div className="p-6 text-center">
                  <p className="text-gray-500">No watches found for "{query}"</p>
                  <Button
                    variant="outline"
                    onClick={() => handleSearch(query)}
                    className="mt-2 text-sm"
                  >
                    Search in catalog
                  </Button>
                </div>
              )}

              {!query && showHistory && searchHistory.length > 0 && (
                <div className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-700 flex items-center">
                      <Clock className="h-4 w-4 mr-2" />
                      Recent searches
                    </h4>
                    <button
                      onClick={clearHistory}
                      className="text-xs text-gray-400 hover:text-gray-600"
                    >
                      Clear
                    </button>
                  </div>
                  {searchHistory.map((historyItem, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setQuery(historyItem);
                        performSearch(historyItem);
                      }}
                      className="block w-full text-left p-2 rounded hover:bg-luxury-cream transition-colors text-gray-600"
                    >
                      {historyItem}
                    </button>
                  ))}
                </div>
              )}

              {!query && (!showHistory || searchHistory.length === 0) && (
                <div className="p-6 text-center">
                  <TrendingUp className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                  <p className="text-gray-500">Start typing to search for luxury watches</p>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
