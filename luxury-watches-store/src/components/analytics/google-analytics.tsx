'use client'

import Script from 'next/script'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect, Suspense } from 'react'

const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_ID

declare global {
  interface Window {
    gtag: (...args: any[]) => void
  }
}

function GoogleAnalyticsContent() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (!GA_MEASUREMENT_ID) return

    const url = pathname + searchParams.toString()
    
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: url,
    })
  }, [pathname, searchParams])

  if (!GA_MEASUREMENT_ID) {
    return null
  }

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_path: window.location.pathname,
            });
          `,
        }}
      />
    </>
  )
}

// E-commerce tracking functions
export const trackPurchase = (transactionId: string, value: number, items: any[]) => {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined') return

  window.gtag('event', 'purchase', {
    transaction_id: transactionId,
    value: value,
    currency: 'USD',
    items: items.map(item => ({
      item_id: item.id,
      item_name: item.name,
      item_brand: item.brand,
      item_category: item.category,
      price: item.price,
      quantity: item.quantity,
    }))
  })
}

export const trackAddToCart = (item: any) => {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined') return

  window.gtag('event', 'add_to_cart', {
    currency: 'USD',
    value: item.price,
    items: [{
      item_id: item.id,
      item_name: item.name,
      item_brand: item.brand,
      item_category: item.category,
      price: item.price,
      quantity: 1,
    }]
  })
}

export const trackRemoveFromCart = (item: any) => {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined') return

  window.gtag('event', 'remove_from_cart', {
    currency: 'USD',
    value: item.price,
    items: [{
      item_id: item.id,
      item_name: item.name,
      item_brand: item.brand,
      item_category: item.category,
      price: item.price,
      quantity: 1,
    }]
  })
}

export const trackViewItem = (item: any) => {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined') return

  window.gtag('event', 'view_item', {
    currency: 'USD',
    value: item.price,
    items: [{
      item_id: item.id,
      item_name: item.name,
      item_brand: item.brand,
      item_category: item.category,
      price: item.price,
    }]
  })
}

export const trackSearch = (searchTerm: string) => {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined') return

  window.gtag('event', 'search', {
    search_term: searchTerm
  })
}

export const trackBeginCheckout = (items: any[], value: number) => {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined') return

  window.gtag('event', 'begin_checkout', {
    currency: 'USD',
    value: value,
    items: items.map(item => ({
      item_id: item.id,
      item_name: item.name,
      item_brand: item.brand,
      item_category: item.category,
      price: item.price,
      quantity: item.quantity,
    }))
  })
}

export function GoogleAnalytics() {
  return (
    <Suspense fallback={null}>
      <GoogleAnalyticsContent />
    </Suspense>
  );
}
