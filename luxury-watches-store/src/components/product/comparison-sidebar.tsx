'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { X, BarChart3, Eye, Trash2 } from 'lucide-react';
import { useComparison } from '@/contexts/comparison-context';
import { useToastActions } from '@/components/ui/toast';
import { Button } from '@/components/ui/button';
import { formatPrice } from '@/lib/utils';
import { getBrandName } from '@/lib/utils/index';

export function ComparisonSidebar() {
  const { items, isOpen, closeComparison, removeItem, clearComparison } = useComparison();
  const { showSuccess } = useToastActions();
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});

  const handleImageError = (watchId: string) => {
    setImageErrors(prev => ({ ...prev, [watchId]: true }));
  };

  const handleRemoveItem = (watchId: string, watchName: string) => {
    removeItem(watchId);
    showSuccess('Removed from comparison', `${watchName} has been removed from comparison`);
  };

  const handleClearAll = () => {
    clearComparison();
    showSuccess('Comparison cleared', 'All watches have been removed from comparison');
  };

  return (
    <>
      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={closeComparison}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-full max-w-md bg-white shadow-2xl z-50 flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <BarChart3 className="h-6 w-6 text-luxury-gold" />
                <div>
                  <h2 className="font-luxury-serif text-xl font-semibold text-luxury-black">
                    Compare Watches
                  </h2>
                  <p className="text-sm text-gray-600">
                    {items.length} of 4 watches
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={closeComparison}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto">
              {items.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full p-6 text-center">
                  <div className="w-16 h-16 bg-luxury-gold/10 rounded-full flex items-center justify-center mb-4">
                    <BarChart3 className="h-8 w-8 text-luxury-gold" />
                  </div>
                  <h3 className="font-medium text-gray-900 mb-2">No watches to compare</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Add watches to your comparison list to see them here
                  </p>
                  <Button variant="outline" onClick={closeComparison}>
                    Continue Shopping
                  </Button>
                </div>
              ) : (
                <div className="p-4 space-y-4">
                  {items.map((watch, index) => (
                    <motion.div
                      key={watch.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center gap-3 p-3 bg-luxury-cream rounded-lg"
                    >
                      <div className="relative w-16 h-16 bg-white rounded-lg overflow-hidden flex-shrink-0">
                        {!imageErrors[watch.id] ? (
                          <Image
                            src={watch.images[0]}
                            alt={`${watch.brand} ${watch.name}`}
                            fill
                            className="object-cover"
                            onError={() => handleImageError(watch.id)}
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full text-luxury-gold text-xl">
                            ⌚
                          </div>
                        )}
                      </div>

                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-luxury-black text-sm truncate">
                          {watch.name}
                        </h4>
                        <p className="text-luxury-gold text-xs font-medium">
                          {getBrandName(watch.brand)}
                        </p>
                        <p className="text-luxury-gold font-semibold text-sm">
                          {formatPrice(watch.price)}
                        </p>
                      </div>

                      <div className="flex flex-col gap-1">
                        <Link href={`/catalog/${watch.id}`} onClick={closeComparison}>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-gray-400 hover:text-luxury-gold"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleRemoveItem(watch.id, watch.name)}
                          className="h-8 w-8 text-gray-400 hover:text-red-500"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {items.length > 0 && (
              <div className="border-t border-gray-200 p-4 space-y-3">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <span>Ready to compare?</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearAll}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    Clear All
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={closeComparison}
                  >
                    Continue Shopping
                  </Button>
                  <Link href="/comparison" className="flex-1" onClick={closeComparison}>
                    <Button variant="luxury" className="w-full">
                      Compare Now
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
