'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Heart, Eye, BarChart3 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useComparison } from '@/contexts/comparison-context';
import { useToastActions } from '@/components/ui/toast';
import { formatPrice, cn } from '@/lib/utils';
import { getBrandName } from '@/lib/utils/index';
import { Button } from '@/components/ui/button';
import { Product, SimpleWatch } from '@/lib/types';
import { getImageUrl } from '@/lib/sanity/utils';

interface WatchCardProps {
  watch: Product | SimpleWatch;
  viewMode?: 'grid' | 'list';
  className?: string;
  onAddToWishlist?: (watchId: string) => void;
  isWishlistItem?: boolean;
}

export function WatchCard({
  watch,
  viewMode = 'grid',
  className,
  onAddToWishlist,
  isWishlistItem = false,
}: WatchCardProps) {
  const [imageError, setImageError] = useState(false);
  const { addItem, openCart } = useCart();
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist();
  const { addItem: addToComparison, removeItem: removeFromComparison, isInComparison, canAddMore } = useComparison();
  const { showSuccess, showWarning } = useToastActions();

  // Handle both Sanity and mock data
  const watchId = (watch as any)._id || watch.id;

  // Use slug if available, otherwise use ID
  const watchUrl = watch.slug?.current || watchId;

  // Handle image URL - check if it's already a string URL or Sanity image object
  let watchImage = '/images/placeholder-watch.svg';
  if (watch.images?.[0]) {
    if (typeof watch.images[0] === 'string') {
      // Mock data - direct URL
      watchImage = watch.images[0];
    } else {
      // Sanity data - use getImageUrl function
      watchImage = getImageUrl(watch.images[0], 400, 400);
    }
  }

  const isAvailable = watch.inStock;

  const handleWishlistToggle = () => {
    if (isInWishlist(watchId)) {
      removeFromWishlist(watchId);
      showSuccess('Removed from wishlist', `${watch.name} has been removed from your wishlist`);
    } else {
      addToWishlist(watch);
      showSuccess('Added to wishlist', `${watch.name} has been added to your wishlist`);
    }
    onAddToWishlist?.(watchId);
  };

  const handleComparisonToggle = () => {
    if (isInComparison(watchId)) {
      removeFromComparison(watchId);
      showSuccess('Removed from comparison', `${watch.name} has been removed from comparison`);
    } else {
      if (canAddMore) {
        const success = addToComparison(watch);
        if (success) {
          showSuccess('Added to comparison', `${watch.name} has been added to comparison`);
        }
      } else {
        showWarning('Comparison full', 'You can compare up to 4 watches at a time');
      }
    }
  };

  const isWatchInWishlist = isWishlistItem || isInWishlist(watchId);
  const isWatchInComparison = isInComparison(watchId);

  const handleAddToCart = () => {
    addItem(watch, 1);
    showSuccess('Added to cart', `${watch.name} has been added to your cart`);
    openCart();
  };

  if (viewMode === 'list') {
    return (
      <div className="bg-white rounded-lg shadow-luxury hover:shadow-luxury-hover transition-all duration-300 overflow-hidden">
        <div className="flex flex-col md:flex-row">
          {/* Image */}
          <div className="relative md:w-80 h-64 md:h-auto bg-luxury-cream flex items-center justify-center">
            {!imageError ? (
              <Image
                src={watchImage}
                alt={`${getBrandName(watch.brand)} ${watch.name}`}
                fill
                className="object-cover"
                onError={() => setImageError(true)}
              />
            ) : (
              <div className="text-luxury-gold text-6xl">⌚</div>
            )}

            {/* Badges */}
            <div className="absolute top-4 left-4 flex flex-col gap-2">
              {watch.isBestseller && (
                <span className="bg-luxury-gold text-luxury-black px-2 py-1 rounded-full text-xs font-semibold">
                  BESTSELLER
                </span>
              )}
              {watch.isNew && (
                <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                  NEW
                </span>
              )}
              {watch.originalPrice && (
                <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                  SALE
                </span>
              )}
            </div>

            {/* Quick Actions */}
            <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleWishlistToggle}
                className={cn(
                  "bg-white/90 hover:bg-white",
                  isWatchInWishlist ? "text-red-500" : "text-gray-600"
                )}
              >
                <Heart className={cn("h-4 w-4", isWatchInWishlist && "fill-current")} />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleComparisonToggle}
                className={cn(
                  "bg-white/90 hover:bg-white",
                  isWatchInComparison ? "text-luxury-gold" : "text-gray-600"
                )}
              >
                <BarChart3 className={cn("h-4 w-4", isWatchInComparison && "fill-current")} />
              </Button>
              <Link href={`/catalog/${watchUrl}`}>
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-white/90 hover:bg-white text-gray-600"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-6">
            <div className="flex justify-between items-start mb-2">
              <div>
                <p className="text-luxury-gold text-sm font-medium">{getBrandName(watch.brand)}</p>
                <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black">
                  {watch.name}
                </h3>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-2">
                  {watch.originalPrice && (
                    <span className="text-gray-400 line-through text-sm">
                      {formatPrice(watch.originalPrice)}
                    </span>
                  )}
                  <span className="text-luxury-gold font-bold text-lg">
                    {formatPrice(watch.price)}
                  </span>
                </div>
              </div>
            </div>

            <p className="text-gray-600 mb-4 line-clamp-2">{watch.description}</p>

            {/* Features */}
            {watch.features && (
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {watch.features.slice(0, 3).map((feature, index) => (
                    <span
                      key={index}
                      className="bg-luxury-cream text-luxury-black px-2 py-1 rounded text-xs"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center gap-3">
              <Button
                onClick={handleAddToCart}
                disabled={!isAvailable}
                className="flex-1"
                variant={isAvailable ? "luxury" : "secondary"}
              >
                {isAvailable ? 'Add to Cart' : 'Out of Stock'}
              </Button>
              <Link href={`/catalog/${watchUrl}`}>
                <Button variant="outline">
                  View Details
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      whileHover={{ y: -5 }}
      className={cn("group bg-white rounded-lg shadow-luxury hover:shadow-luxury-hover transition-all duration-300 overflow-hidden", className)}
    >
      {/* Image */}
      <div className="relative h-64 bg-luxury-cream flex items-center justify-center overflow-hidden">
        {!imageError ? (
          <Image
            src={watchImage}
            alt={`${getBrandName(watch.brand)} ${watch.name}`}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="text-luxury-gold text-6xl">⌚</div>
        )}

        {/* Badges */}
        <div className="absolute top-4 left-4 flex flex-col gap-2">
          {watch.isBestseller && (
            <span className="bg-luxury-gold text-luxury-black px-2 py-1 rounded-full text-xs font-semibold">
              BESTSELLER
            </span>
          )}
          {watch.isNew && (
            <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
              NEW
            </span>
          )}
          {watch.originalPrice && (
            <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
              SALE
            </span>
          )}
        </div>

        {/* Quick Actions */}
        <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleWishlistToggle}
            className={cn(
              "bg-white/90 hover:bg-white",
              isWatchInWishlist ? "text-red-500" : "text-gray-600"
            )}
          >
            <Heart className={cn("h-4 w-4", isWatchInWishlist && "fill-current")} />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleComparisonToggle}
            className={cn(
              "bg-white/90 hover:bg-white",
              isWatchInComparison ? "text-luxury-gold" : "text-gray-600"
            )}
          >
            <BarChart3 className={cn("h-4 w-4", isWatchInComparison && "fill-current")} />
          </Button>
          <Link href={`/catalog/${watchUrl}`}>
            <Button
              variant="ghost"
              size="icon"
              className="bg-white/90 hover:bg-white text-gray-600"
            >
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
        </div>

        {/* Overlay with quick add to cart */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-end p-4">
          <Button
            onClick={handleAddToCart}
            disabled={!isAvailable}
            className="w-full"
            variant={isAvailable ? "luxury" : "secondary"}
          >
            {isAvailable ? 'Add to Cart' : 'Out of Stock'}
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <div className="mb-2">
          <p className="text-luxury-gold text-sm font-medium">{getBrandName(watch.brand)}</p>
          <h3 className="font-luxury-serif text-lg font-semibold text-luxury-black line-clamp-1">
            {watch.name}
          </h3>
        </div>

        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{watch.description}</p>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {watch.originalPrice && (
              <span className="text-gray-400 line-through text-sm">
                {formatPrice(watch.originalPrice)}
              </span>
            )}
            <span className="text-luxury-gold font-bold">
              {formatPrice(watch.price)}
            </span>
          </div>

          <Link href={`/catalog/${watchUrl}`}>
            <Button variant="ghost" size="sm" className="text-luxury-gold hover:text-luxury-gold-dark">
              View Details →
            </Button>
          </Link>
        </div>
      </div>
    </motion.div>
  );
}
