import { StructureBuilder } from 'sanity/structure'
import {
  CogIcon,
  DocumentTextIcon,
  PhotoIcon,
  TagIcon,
  UserIcon,
  ShoppingBagIcon,
  StarIcon,
  BookOpenIcon,
  ChartBarIcon,
  GlobeAltIcon,
  HomeIcon,
  PhotoIcon as ImageIcon
} from '@heroicons/react/24/outline'
import { AnalyticsDashboard } from './components/analytics-dashboard'
import { MediaLibrary } from './components/media-library'
import { DashboardWidget } from './components/dashboard-widget'

export const structure = (S: StructureBuilder) =>
  S.list()
    .title('⚜️ Atlas Luxury CMS')
    .items([
      // Dashboard
      S.listItem()
        .title('📊 Dashboard')
        .icon(HomeIcon)
        .child(
          S.component(DashboardWidget).title('Dashboard Overview')
        ),

      S.divider(),

      // Site Settings (Singleton)
      S.listItem()
        .title('⚙️ Site Settings')
        .icon(CogIcon)
        .child(
          S.document()
            .schemaType('siteSettings')
            .documentId('siteSettings')
            .title('Site Configuration')
        ),

      S.divider(),

      // Products Section
      S.listItem()
        .title('📦 Products')
        .icon(ShoppingBagIcon)
        .child(
          S.list()
            .title('Product Management')
            .items([
              S.listItem()
                .title('📋 All Products')
                .child(
                  S.documentTypeList('product')
                    .title('All Products')
                    .filter('_type == "product"')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('✅ Published Products')
                .child(
                  S.documentTypeList('product')
                    .title('Published Products')
                    .filter('_type == "product" && isAvailable == true')
                    .defaultOrdering([{ field: '_updatedAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('📝 Draft Products')
                .child(
                  S.documentTypeList('product')
                    .title('Draft Products')
                    .filter('_type == "product" && isAvailable != true')
                    .defaultOrdering([{ field: '_updatedAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('⭐ Featured Products')
                .child(
                  S.documentTypeList('product')
                    .title('Featured Products')
                    .filter('_type == "product" && isFeatured == true')
                    .defaultOrdering([{ field: '_updatedAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('🆕 New Arrivals')
                .child(
                  S.documentTypeList('product')
                    .title('New Arrivals')
                    .filter('_type == "product" && isNew == true')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
              S.divider(),
              S.listItem()
                .title('⌚ Watches')
                .child(
                  S.documentTypeList('product')
                    .title('Watches')
                    .filter('_type == "product" && productType == "watch"')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('💎 Jewelry')
                .child(
                  S.documentTypeList('product')
                    .title('Jewelry')
                    .filter('_type == "product" && productType == "jewelry"')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
              S.divider(),
              S.listItem()
                .title('⚠️ Out of Stock')
                .child(
                  S.documentTypeList('product')
                    .title('Out of Stock')
                    .filter('_type == "product" && inventory.stock <= 0')
                    .defaultOrdering([{ field: '_updatedAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('🔍 Low Stock Alert')
                .child(
                  S.documentTypeList('product')
                    .title('Low Stock Products')
                    .filter('_type == "product" && inventory.stock > 0 && inventory.stock <= inventory.lowStockThreshold')
                    .defaultOrdering([{ field: 'inventory.stock', direction: 'asc' }])
                ),
            ])
        ),

      // Catalog Organization
      S.listItem()
        .title('🏷️ Catalog')
        .icon(TagIcon)
        .child(
          S.list()
            .title('Catalog Organization')
            .items([
              S.listItem()
                .title('🏢 Brands')
                .child(
                  S.documentTypeList('brand')
                    .title('Luxury Brands')
                    .defaultOrdering([{ field: 'name', direction: 'asc' }])
                ),
              S.listItem()
                .title('📚 Collections')
                .child(
                  S.documentTypeList('collection')
                    .title('Product Collections')
                    .defaultOrdering([{ field: 'sortOrder', direction: 'asc' }])
                ),
              S.listItem()
                .title('📂 Categories')
                .child(
                  S.documentTypeList('category')
                    .title('Product Categories')
                    .defaultOrdering([{ field: 'sortOrder', direction: 'asc' }])
                ),
            ])
        ),

      S.divider(),

      // Content Section
      S.listItem()
        .title('📝 Content')
        .icon(DocumentTextIcon)
        .child(
          S.list()
            .title('Content Management')
            .items([
              S.listItem()
                .title('🎯 Landing Pages')
                .child(
                  S.documentTypeList('landingPage')
                    .title('Landing Pages')
                    .filter('_type == "landingPage"')
                    .defaultOrdering([{ field: '_updatedAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('📄 Custom Pages')
                .child(
                  S.documentTypeList('page')
                    .title('Custom Pages')
                    .filter('_type == "page"')
                    .defaultOrdering([{ field: '_updatedAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('✍️ Blog Posts')
                .child(
                  S.documentTypeList('blogPost')
                    .title('Blog Posts')
                    .filter('_type == "blogPost"')
                    .defaultOrdering([{ field: 'publishedAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('📖 Published Posts')
                .child(
                  S.documentTypeList('blogPost')
                    .title('Published Blog Posts')
                    .filter('_type == "blogPost" && isPublished == true')
                    .defaultOrdering([{ field: 'publishedAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('📷 Lookbooks')
                .child(
                  S.documentTypeList('lookbook')
                    .title('Lookbooks')
                    .filter('_type == "lookbook"')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
            ])
        ),

      // Media Section
      S.listItem()
        .title('🖼️ Media Library')
        .icon(PhotoIcon)
        .child(
          S.list()
            .title('Media Management')
            .items([
              S.listItem()
                .title('🎨 Media Browser')
                .icon(ImageIcon)
                .child(
                  S.component(MediaLibrary).title('Media Library Browser')
                ),
              S.listItem()
                .title('📸 All Images')
                .child(
                  S.documentTypeList('sanity.imageAsset')
                    .title('All Images')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('📦 Product Images')
                .child(
                  S.documentTypeList('sanity.imageAsset')
                    .title('Product Images')
                    .filter('_type == "sanity.imageAsset"')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('🏢 Brand Assets')
                .child(
                  S.documentTypeList('sanity.imageAsset')
                    .title('Brand Assets')
                    .filter('_type == "sanity.imageAsset"')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('🗑️ Unused Images')
                .child(
                  S.documentTypeList('sanity.imageAsset')
                    .title('Unused Images')
                    .filter('_type == "sanity.imageAsset"')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
            ])
        ),

      S.divider(),

      // Customer Engagement
      S.listItem()
        .title('👥 Customer Engagement')
        .icon(UserIcon)
        .child(
          S.list()
            .title('Customer Content')
            .items([
              S.listItem()
                .title('⭐ All Reviews')
                .child(
                  S.documentTypeList('review')
                    .title('Customer Reviews')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('✅ Approved Reviews')
                .child(
                  S.documentTypeList('review')
                    .title('Approved Reviews')
                    .filter('_type == "review" && isApproved == true')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
              S.listItem()
                .title('🌟 Featured Testimonials')
                .child(
                  S.documentTypeList('review')
                    .title('Featured Testimonials')
                    .filter('_type == "review" && isFeatured == true')
                    .defaultOrdering([{ field: 'rating', direction: 'desc' }])
                ),
              S.listItem()
                .title('⏳ Pending Reviews')
                .child(
                  S.documentTypeList('review')
                    .title('Pending Reviews')
                    .filter('_type == "review" && isApproved != true')
                    .defaultOrdering([{ field: '_createdAt', direction: 'desc' }])
                ),
            ])
        ),

      // Analytics & Reports
      S.listItem()
        .title('📊 Analytics')
        .icon(ChartBarIcon)
        .child(
          S.component(AnalyticsDashboard).title('Analytics Dashboard')
        ),

      S.divider(),

      // Localization
      S.listItem()
        .title('Localization')
        .icon(GlobeAltIcon)
        .child(
          S.list()
            .title('Localization Management')
            .items([
              S.listItem()
                .title('English Content')
                .child(
                  S.list()
                    .title('English Content')
                    .items([
                      S.documentTypeList('product').title('Products (EN)').filter('language == "en"'),
                      S.documentTypeList('brand').title('Brands (EN)').filter('language == "en"'),
                      S.documentTypeList('collection').title('Collections (EN)').filter('language == "en"'),
                      S.documentTypeList('blogPost').title('Blog Posts (EN)').filter('language == "en"')
                    ])
                ),
              S.listItem()
                .title('German Content')
                .child(
                  S.list()
                    .title('German Content')
                    .items([
                      S.documentTypeList('product').title('Products (DE)').filter('language == "de"'),
                      S.documentTypeList('brand').title('Brands (DE)').filter('language == "de"'),
                      S.documentTypeList('collection').title('Collections (DE)').filter('language == "de"'),
                      S.documentTypeList('blogPost').title('Blog Posts (DE)').filter('language == "de"')
                    ])
                ),
              S.listItem()
                .title('French Content')
                .child(
                  S.list()
                    .title('French Content')
                    .items([
                      S.documentTypeList('product').title('Products (FR)').filter('language == "fr"'),
                      S.documentTypeList('brand').title('Brands (FR)').filter('language == "fr"'),
                      S.documentTypeList('collection').title('Collections (FR)').filter('language == "fr"'),
                      S.documentTypeList('blogPost').title('Blog Posts (FR)').filter('language == "fr"')
                    ])
                ),
              S.listItem()
                .title('Translation Status')
                .child(
                  S.list()
                    .title('Needs Translation')
                    .items([
                      S.documentTypeList('product').title('Products - Needs Translation').filter('!defined(translations)'),
                      S.documentTypeList('brand').title('Brands - Needs Translation').filter('!defined(translations)'),
                      S.documentTypeList('collection').title('Collections - Needs Translation').filter('!defined(translations)'),
                      S.documentTypeList('blogPost').title('Blog Posts - Needs Translation').filter('!defined(translations)')
                    ])
                ),
            ])
        ),
    ])

export default structure
