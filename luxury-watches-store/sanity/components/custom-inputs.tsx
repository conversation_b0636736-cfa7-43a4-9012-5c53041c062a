import React, { useState } from 'react'
import { Card, Text, Flex, Stack, Button, TextInput, Badge } from '@sanity/ui'
import { PatchEvent, set, unset } from 'sanity'

// Types for component props
interface PriceInputProps {
  value?: number
  onChange: (event: PatchEvent) => void
  elementProps?: Record<string, unknown>
  schemaType: {
    title?: string
  }
}

interface StockInputProps {
  value?: number
  onChange: (event: PatchEvent) => void
  elementProps?: Record<string, unknown>
}

interface SEOInputProps {
  value?: string
  onChange: (event: PatchEvent) => void
  elementProps?: Record<string, unknown>
}

interface TagInputProps {
  value?: string[]
  onChange: (event: PatchEvent) => void
  elementProps?: Record<string, unknown>
}

interface SEOAnalysis {
  score: number
  issues: string[]
  charCount: number
  wordCount: number
}

type StockTone = 'critical' | 'caution' | 'primary' | 'positive'

interface StockStatus {
  color: string
  text: string
  tone: StockTone
}

// Enhanced Price Input with Currency Support
export function PriceInput(props: PriceInputProps) {
  const { value, onChange, elementProps, schemaType } = props
  const [currency, setCurrency] = useState('USD')
  
  const handleChange = (newValue: string) => {
    const numericValue = newValue ? parseFloat(newValue) : undefined
    onChange(PatchEvent.from(numericValue ? set(numericValue) : unset()))
  }

  return (
    <Card padding={3} border radius={2}>
      <Stack space={3}>
        <Flex align="center" gap={2}>
          <Text size={1} weight="medium">
            {schemaType.title || 'Price'}
          </Text>
          <Badge tone="primary">{currency}</Badge>
        </Flex>
        
        <Flex align="center" gap={2}>
          <div style={{ position: 'relative', flex: 1 }}>
            <span style={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#666',
              fontSize: '16px',
              fontWeight: 'bold',
              pointerEvents: 'none'
            }}>
              $
            </span>
            <TextInput
              {...elementProps}
              type="number"
              value={value || ''}
              onChange={(e) => handleChange(e.currentTarget.value)}
              style={{ paddingLeft: '28px' }}
              placeholder="0.00"
              min="0"
              step="0.01"
            />
          </div>
          
          <select
            value={currency}
            onChange={(e) => setCurrency(e.target.value)}
            style={{
              padding: '8px 12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              background: 'white'
            }}
          >
            <option value="USD">USD</option>
            <option value="EUR">EUR</option>
            <option value="GBP">GBP</option>
            <option value="JPY">JPY</option>
          </select>
        </Flex>
        
        {value && (
          <Text size={1} muted>
            Formatted: ${value.toLocaleString()}
          </Text>
        )}
      </Stack>
    </Card>
  )
}

// Stock Level Input with Visual Indicators
export function StockInput(props: StockInputProps) {
  const { value, onChange, elementProps } = props
  const stock = value || 0

  const getStockStatus = (): StockStatus => {
    if (stock === 0) return { color: '#cc0000', text: 'Out of Stock', tone: 'critical' }
    if (stock <= 5) return { color: '#ff9900', text: 'Low Stock', tone: 'caution' }
    if (stock <= 20) return { color: '#0066cc', text: 'Medium Stock', tone: 'primary' }
    return { color: '#00cc00', text: 'Good Stock', tone: 'positive' }
  }

  const status = getStockStatus()

  const handleChange = (newValue: string) => {
    const numericValue = newValue ? parseInt(newValue, 10) : undefined
    onChange(PatchEvent.from(numericValue !== undefined ? set(numericValue) : unset()))
  }

  return (
    <Card padding={3} border radius={2}>
      <Stack space={3}>
        <Flex align="center" justify="space-between">
          <Text size={1} weight="medium">
            Stock Quantity
          </Text>
          <Badge tone={status.tone}>{status.text}</Badge>
        </Flex>
        
        <TextInput
          {...elementProps}
          type="number"
          value={value || ''}
          onChange={(e) => handleChange(e.currentTarget.value)}
          placeholder="0"
          min="0"
          step="1"
        />
        
        <Flex align="center" gap={2}>
          <div style={{
            width: '12px',
            height: '12px',
            borderRadius: '50%',
            backgroundColor: status.color
          }} />
          <Text size={1} style={{ color: status.color }}>
            {stock} units available
          </Text>
        </Flex>
        
        {stock <= 5 && stock > 0 && (
          <Card padding={2} tone="caution">
            <Text size={1}>
              ⚠️ Low stock alert! Consider restocking soon.
            </Text>
          </Card>
        )}
      </Stack>
    </Card>
  )
}

// SEO Score Input with Real-time Analysis
export function SEOInput(props: SEOInputProps) {
  const { value, onChange, elementProps } = props
  const [analysis, setAnalysis] = useState<SEOAnalysis | null>(null)

  const analyzeSEO = (text: string) => {
    const wordCount = text.split(' ').length
    const charCount = text.length
    
    let score = 0
    const issues = []
    
    // Length checks
    if (charCount >= 50 && charCount <= 60) score += 30
    else if (charCount < 50) issues.push('Too short (recommended: 50-60 characters)')
    else if (charCount > 60) issues.push('Too long (recommended: 50-60 characters)')
    
    // Word count
    if (wordCount >= 5 && wordCount <= 10) score += 20
    else issues.push('Optimal word count: 5-10 words')
    
    // Keywords
    if (text.toLowerCase().includes('luxury')) score += 15
    if (text.toLowerCase().includes('watch') || text.toLowerCase().includes('jewelry')) score += 15
    if (text.toLowerCase().includes('premium') || text.toLowerCase().includes('exclusive')) score += 10
    
    // Capitalization
    if (text.charAt(0) === text.charAt(0).toUpperCase()) score += 10
    else issues.push('Should start with capital letter')
    
    return { score, issues, charCount, wordCount }
  }

  const handleChange = (newValue: string) => {
    onChange(PatchEvent.from(newValue ? set(newValue) : unset()))
    if (newValue) {
      setAnalysis(analyzeSEO(newValue))
    } else {
      setAnalysis(null)
    }
  }

  React.useEffect(() => {
    if (value) {
      setAnalysis(analyzeSEO(value))
    }
  }, [value])

  const getScoreColor = (score: number) => {
    if (score >= 80) return '#00cc00'
    if (score >= 60) return '#ff9900'
    return '#cc0000'
  }

  return (
    <Card padding={3} border radius={2}>
      <Stack space={3}>
        <Flex align="center" justify="space-between">
          <Text size={1} weight="medium">
            SEO Title
          </Text>
          {analysis && (
            <Badge
              tone={analysis.score >= 80 ? 'positive' : analysis.score >= 60 ? 'caution' : 'critical'}
            >
              Score: {analysis.score}/100
            </Badge>
          )}
        </Flex>
        
        <TextInput
          {...elementProps}
          value={value || ''}
          onChange={(e) => handleChange(e.currentTarget.value)}
          placeholder="Enter SEO-optimized title..."
        />
        
        {analysis && (
          <Stack space={2}>
            <Flex align="center" gap={3}>
              <Text size={1}>
                Characters: {analysis.charCount}/60
              </Text>
              <Text size={1}>
                Words: {analysis.wordCount}
              </Text>
            </Flex>
            
            <div style={{
              width: '100%',
              height: '4px',
              backgroundColor: '#f0f0f0',
              borderRadius: '2px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${Math.min(analysis.score, 100)}%`,
                height: '100%',
                backgroundColor: getScoreColor(analysis.score),
                transition: 'width 0.3s ease'
              }} />
            </div>
            
            {analysis.issues.length > 0 && (
              <Card padding={2} tone="caution">
                <Stack space={1}>
                  <Text size={1} weight="medium">
                    Suggestions:
                  </Text>
                  {analysis.issues.map((issue: string, index: number) => (
                    <Text key={index} size={1}>
                      • {issue}
                    </Text>
                  ))}
                </Stack>
              </Card>
            )}
          </Stack>
        )}
      </Stack>
    </Card>
  )
}

// Tag Input with Suggestions
export function TagInput(props: TagInputProps) {
  const { value = [], onChange } = props
  const [inputValue, setInputValue] = useState('')
  
  const suggestions = [
    'luxury', 'premium', 'exclusive', 'limited-edition', 'swiss-made',
    'automatic', 'mechanical', 'chronograph', 'diving', 'dress-watch',
    'gold', 'steel', 'titanium', 'ceramic', 'leather', 'bracelet',
    'vintage', 'modern', 'classic', 'sporty', 'elegant'
  ]

  const addTag = (tag: string) => {
    if (tag && !value.includes(tag)) {
      const newTags = [...value, tag]
      onChange(PatchEvent.from(set(newTags)))
    }
    setInputValue('')
  }

  const removeTag = (tagToRemove: string) => {
    const newTags = value.filter((tag: string) => tag !== tagToRemove)
    onChange(PatchEvent.from(set(newTags)))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      addTag(inputValue.trim())
    }
  }

  const filteredSuggestions = suggestions.filter(
    suggestion => 
      suggestion.toLowerCase().includes(inputValue.toLowerCase()) &&
      !value.includes(suggestion)
  )

  return (
    <Card padding={3} border radius={2}>
      <Stack space={3}>
        <Text size={1} weight="medium">
          Tags
        </Text>
        
        {/* Current Tags */}
        {value.length > 0 && (
          <Flex gap={1} wrap="wrap">
            {value.map((tag: string) => (
              <Badge
                key={tag}
                tone="primary"
                style={{ cursor: 'pointer' }}
                onClick={() => removeTag(tag)}
              >
                {tag}
              </Badge>
            ))}
          </Flex>
        )}
        
        {/* Input */}
        <TextInput
          value={inputValue}
          onChange={(e) => setInputValue(e.currentTarget.value)}
          onKeyDown={handleKeyPress}
          placeholder="Type tags and press Enter..."
        />
        
        {/* Suggestions */}
        {inputValue && filteredSuggestions.length > 0 && (
          <Card padding={2} tone="transparent" border>
            <Stack space={1}>
              <Text size={1} weight="medium">
                Suggestions:
              </Text>
              <Flex gap={1} wrap="wrap">
                {filteredSuggestions.slice(0, 8).map(suggestion => (
                  <Button
                    key={suggestion}
                    text={suggestion}
                    mode="ghost"
                    tone="primary"
                    fontSize={1}
                    onClick={() => addTag(suggestion)}
                  />
                ))}
              </Flex>
            </Stack>
          </Card>
        )}
      </Stack>
    </Card>
  )
}
